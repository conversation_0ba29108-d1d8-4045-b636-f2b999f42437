// 文件上传相关API
import { upload } from "@/utils/request";

// 上传文件
export const uploadFile = (filePath, name = "file") => {
  return upload("/common/upload", filePath, name);
};

// 上传图片
export const uploadImage = (filePath) => {
  return uploadFile(filePath, "file");
};

// 批量上传图片
export const uploadImages = (filePaths) => {
  const promises = filePaths.map((filePath) => uploadImage(filePath));
  return Promise.all(promises);
};
