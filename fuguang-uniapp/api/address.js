// 地址管理相关API
import { get, post, put, del } from "@/utils/request";

// 获取当前用户地址列表
export const getAddressList = () => {
  return get("/app/address/list");
};

// 获取地址详情
export const getAddressDetail = (addressId) => {
  return get(`/app/address/${addressId}`);
};

// 新增地址
export const addAddress = (data) => {
  return post("/app/address", data);
};

// 修改地址
export const updateAddress = (data) => {
  return put("/app/address", data);
};

// 删除地址
export const deleteAddress = (addressId) => {
  return del(`/app/address/${addressId}`);
};

// 设置默认地址
export const setDefaultAddress = (addressId) => {
  return put(`/app/address/setDefault/${addressId}`);
};

// 获取默认地址
export const getDefaultAddress = () => {
  return get("/app/address/default");
};

// 根据定位信息解析地址
export const parseLocation = (data) => {
  return post("/app/address/parseLocation", data);
};

// 获取当前位置
export const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type: 'gcj02',
      success: (res) => {
        resolve({
          longitude: res.longitude,
          latitude: res.latitude,
          address: res.address || ''
        });
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
        uni.showToast({
          title: '获取位置失败',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

// 选择位置（调用地图选择）
export const chooseLocation = () => {
  return new Promise((resolve, reject) => {
    uni.chooseLocation({
      success: (res) => {
        resolve({
          name: res.name,
          address: res.address,
          longitude: res.longitude,
          latitude: res.latitude
        });
      },
      fail: (err) => {
        console.error('选择位置失败:', err);
        if (err.errMsg && err.errMsg.includes('cancel')) {
          // 用户取消选择
          reject({ type: 'cancel', message: '用户取消选择' });
        } else {
          uni.showToast({
            title: '选择位置失败',
            icon: 'none'
          });
          reject(err);
        }
      }
    });
  });
};
