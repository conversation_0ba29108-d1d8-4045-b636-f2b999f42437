// 首页相关API
import { get, post } from "@/utils/request";

// 获取首页数据
export const getHomeData = (params) => {
  return get("/app/home/<USER>", params);
};

// 搜索
export const search = (params) => {
  return get("/app/home/<USER>", params);
};

// 获取通知列表
export const getNotices = (params) => {
  return get("/app/home/<USER>", params);
};

// 标记通知为已读
export const markNoticeAsRead = (noticeId) => {
  return get("/app/home/<USER>/read", { noticeId });
};

// 获取未读通知数量
export const getUnreadNoticeCount = () => {
  return get("/app/home/<USER>/unread-count");
};

// 批量标记通知为已读
export const markAllNoticesAsRead = () => {
  return post("/app/home/<USER>/mark-all-read");
};

// 清空所有通知
export const clearAllNotices = () => {
  return post("/app/home/<USER>/clear-all");
};

// 获取APP配置
export const getAppConfig = () => {
  return get("/app/home/<USER>");
};

// 二维码扫描处理
export const scanQrCode = (content) => {
  return post("/app/home/<USER>/scan", { content });
};

// 获取兴业助农配置
export const getAgricultureConfig = () => {
  return get("/app/home/<USER>");
};

// 获取购物专区配置
export const getShoppingConfig = () => {
  return get("/app/home/<USER>");
};

// 获取更多功能配置
export const getFunctions = () => {
  return get("/app/home/<USER>");
};
