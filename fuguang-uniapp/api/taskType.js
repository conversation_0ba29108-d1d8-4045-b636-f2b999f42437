// 任务类型相关API
import { get } from '@/utils/request'

// 获取任务类型列表
export const getTaskTypeList = (params) => {
  return get('/app/taskType/list', params)
}

// 获取任务类型详情
export const getTaskTypeDetail = (typeId) => {
  return get(`/app/taskType/${typeId}`)
}

// 获取一级任务类型列表
export const getFirstLevelTaskTypes = () => {
  return get('/app/taskType/firstLevel')
}

// 根据父类型ID获取子类型列表
export const getChildrenTaskTypes = (parentId) => {
  return get(`/app/taskType/children/${parentId}`)
}

// 获取任务类型树形结构
export const getTaskTypeTree = () => {
  return get('/app/taskType/tree')
}
