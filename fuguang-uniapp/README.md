# 浮光壁垒 UniApp 前端项目

## 项目简介

浮光壁垒是一个基于 UniApp 开发的跨平台移动应用，主要功能包括任务发布与接取、用户管理、消息通知等。项目采用 Vue2 + uView UI 框架开发，支持微信小程序、H5、App 等多端运行。

## 技术栈

- **框架**: UniApp (Vue2)
- **UI 库**: uView UI
- **状态管理**: Vuex
- **网络请求**: 封装的 request 工具
- **开发工具**: HBuilderX

## 项目结构

```
fuguang-uniapp/
├── api/                    # API接口封装
│   ├── auth.js            # 认证相关接口
│   ├── home.js            # 首页相关接口
│   └── task.js            # 任务相关接口
├── components/            # 公共组件
│   └── TaskCard/          # 任务卡片组件
├── config/                # 配置文件
│   └── index.js          # 环境配置
├── pages/                 # 页面文件
│   ├── index/            # 首页
│   ├── login/            # 登录页
│   ├── register/         # 注册页
│   ├── task/             # 任务相关页面
│   ├── user/             # 用户相关页面
│   ├── notice/           # 通知页面
│   └── agreement/        # 协议页面
├── static/               # 静态资源
├── store/                # Vuex状态管理
├── utils/                # 工具函数
│   ├── request.js        # 网络请求封装
│   └── common.js         # 通用工具函数
├── App.vue               # 应用入口
├── main.js               # 主入口文件
├── manifest.json         # 应用配置
├── pages.json            # 页面配置
└── uni.scss              # 全局样式
```

## 主要功能

### 1. 用户认证

- 用户注册/登录
- 实名认证
- 密码修改
- 头像上传

#### 登录页面设计特点

**🎨 视觉设计**

- **渐变背景**: 采用紫色到蓝色的渐变背景，营造科技感
- **动态装饰**: 浮动的圆形装饰元素，增加页面活力
- **波浪效果**: 顶部波浪装饰，增强视觉层次
- **毛玻璃效果**: 登录表单采用毛玻璃背景，现代感十足

**✨ 交互体验**

- **动画效果**: Logo 发光动画、浮动元素动画、按钮点击反馈
- **响应式设计**: 适配不同屏幕尺寸
- **微交互**: 细腻的过渡动画和状态反馈

**🔧 功能特性**

- **多种登录方式**: 用户名/手机号登录、微信登录、游客模式
- **安全性**: 密码输入保护、用户协议确认、表单验证
- **用户体验**: 忘记密码功能、一键注册跳转、加载状态提示

### 2. 首页功能

- 首页标语展示
- 位置定位
- 搜索功能
- 二维码扫描
- 系统通知滚动
- 兴业助农板块
- 购物专区
- 热门任务展示

### 3. 任务管理

- 任务列表浏览
- 任务详情查看
- 任务发布
- 任务接取
- 任务完成
- 我的任务管理

### 4. 用户中心

- 个人信息管理
- 实名认证
- 消息通知
- 设置功能

### 5. 通知系统

- 系统通知
- 活动通知
- 任务通知
- 未读消息提醒

## 开发环境搭建

### 1. 安装依赖

```bash
npm install
```

### 2. 运行项目

#### 微信小程序

```bash
npm run dev:mp-weixin
```

#### H5

```bash
npm run dev:h5
```

#### App

```bash
npm run dev:app-plus
```

### 3. 构建项目

#### 微信小程序

```bash
npm run build:mp-weixin
```

#### H5

```bash
npm run build:h5
```

#### App

```bash
npm run build:app-plus
```

## 配置说明

### 1. 环境配置

在 `config/index.js` 中配置不同环境的 API 地址：

```javascript
const config = {
  development: {
    baseURL: "http://localhost:8888",
    timeout: 10000,
    debug: true,
  },
  production: {
    baseURL: "https://api.fuguang.com",
    timeout: 10000,
    debug: false,
  },
};
```

### 2. 页面配置

在 `pages.json` 中配置页面路由和 tabBar：

```json
{
  "pages": [...],
  "tabBar": {
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页"
      }
    ]
  }
}
```

## API 接口

### 1. 认证接口

- `POST /app/login` - 用户登录
- `POST /app/register` - 用户注册
- `GET /app/user/profile` - 获取用户信息
- `PUT /app/user/profile` - 更新用户信息

### 2. 首页接口

- `GET /app/home/<USER>
- `GET /app/home/<USER>
- `GET /app/home/<USER>

### 3. 任务接口

- `GET /app/task/list` - 获取任务列表
- `GET /app/task/{id}` - 获取任务详情
- `POST /app/task` - 发布任务
- `POST /app/task/accept/{id}` - 接取任务

## 状态管理

使用 Vuex 管理全局状态：

```javascript
// 用户信息
this.$store.state.userInfo;
this.$store.getters.isLoggedIn;

// 位置信息
this.$store.state.location;
this.$store.getters.currentLocation;

// 未读消息
this.$store.state.unreadCount;
this.$store.getters.hasUnreadMessage;
```

## 工具函数

### 1. 网络请求

```javascript
import { get, post } from "@/utils/request";

// GET请求
const data = await get("/api/endpoint");

// POST请求
const result = await post("/api/endpoint", { data });
```

### 2. 通用工具

```javascript
import { formatTime, formatMoney, getCurrentLocation } from "@/utils/common";

// 格式化时间
const timeStr = formatTime(new Date());

// 格式化金额
const moneyStr = formatMoney(100.5);

// 获取当前位置
const location = await getCurrentLocation();
```

## 注意事项

1. **权限配置**: 确保在 manifest.json 中配置了必要的权限（位置、相机、相册等）
2. **网络安全**: 生产环境需要配置 HTTPS
3. **小程序审核**: 发布微信小程序需要通过审核
4. **兼容性**: 注意不同平台的 API 差异

## 部署说明

### 1. 微信小程序

1. 使用微信开发者工具打开 dist/dev/mp-weixin 目录
2. 配置 AppID
3. 上传代码并提交审核

### 2. H5 部署

1. 构建 H5 版本：`npm run build:h5`
2. 将 dist/build/h5 目录部署到 Web 服务器

### 3. App 打包

1. 使用 HBuilderX 云打包
2. 配置证书和包名
3. 生成安装包

## 开发规范

1. **代码风格**: 使用 ESLint 和 Prettier 保持代码风格一致
2. **组件命名**: 使用 PascalCase 命名组件
3. **文件命名**: 使用 kebab-case 命名文件
4. **提交规范**: 使用 Conventional Commits 规范

## 常见问题

### 1. 网络请求失败

- 检查 API 地址配置
- 确认网络连接
- 查看控制台错误信息

### 2. 页面跳转异常

- 检查 pages.json 配置
- 确认路由路径正确
- 查看页面是否存在

### 3. 组件样式问题

- 检查 CSS 作用域
- 确认 uView 组件使用正确
- 查看样式优先级

## 更新日志

### v1.2.0 (2025-01-19)

**地址选择和任务类型功能优化**

- 🐛 修复地址选择弹框无法正常显示的问题
- ✨ 优化 AddressSelector 组件，增加 z-index 设置和错误处理
- ✨ 完善任务类型二级联动功能，数据从管理后台获取
- ✨ 新增测试页面 (`/pages/test/address-task-type.vue`) 用于功能验证
- 🔧 添加详细的调试日志便于问题排查
- 🔧 优化任务发布页面的表单验证逻辑
- 🔧 改进用户交互体验和错误提示

**功能特性**

- **地址选择**: 支持当前位置、地图选点、已保存地址选择
- **任务类型**: 实现一级/二级类型联动选择
- **数据同步**: 任务类型数据从管理后台实时获取
- **错误处理**: 完善的错误提示和异常处理机制

### v1.1.0 (2025-01-18)

**设置页面重构**

- ✨ 新增用户信息展示区域（头像、昵称、手机号）
- ✨ 新增账号管理功能（实名认证、忘记密码、注销账号）
- ✨ 新增应用设置功能（检查更新、关于浮光、帮助反馈）
- ✨ 新增关于浮光页面 (`/pages/about/index.vue`)
- ✨ 新增帮助反馈页面 (`/pages/feedback/index.vue`)
- ✨ 新增忘记密码页面 (`/pages/user/forget-password.vue`)
- 🔧 优化设置页面 UI 设计和用户体验
- 🔧 完善 API 接口（重置密码、注销账号）

### v1.0.0 (2025-01-18)

- 初始版本发布
- 完成基础功能开发
- 支持多端运行

## 联系方式

- 开发团队：浮光壁垒开发组
- 邮箱：<EMAIL>
- 技术支持：400-888-8888
