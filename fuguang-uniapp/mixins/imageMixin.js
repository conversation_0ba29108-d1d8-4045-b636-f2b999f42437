// 图片处理混入
import { getImageUrl } from '@/utils/request'

export default {
  methods: {
    // 获取图片完整URL
    getImageUrl,
    
    // 处理图片数组
    getImageUrls(imageArray) {
      if (!imageArray || !Array.isArray(imageArray)) {
        return []
      }
      return imageArray.map(img => this.getImageUrl(img))
    },
    
    // 处理逗号分隔的图片字符串
    getImageUrlsFromString(imageString) {
      if (!imageString) {
        return []
      }
      const imageArray = imageString.split(',').filter(img => img.trim())
      return this.getImageUrls(imageArray)
    }
  }
}
