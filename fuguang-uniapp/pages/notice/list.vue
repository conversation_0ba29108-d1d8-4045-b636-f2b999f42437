<template>
  <view class="notice-container">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" :class="{ active: currentFilter === 'all' }" @click="setFilter('all')">
        全部
      </view>
      <view class="filter-item" :class="{ active: currentFilter === '0' }" @click="setFilter('0')">
        系统通知
      </view>
      <view class="filter-item" :class="{ active: currentFilter === '1' }" @click="setFilter('1')">
        活动通知
      </view>
      <view class="filter-item" :class="{ active: currentFilter === '2' }" @click="setFilter('2')">
        任务通知
      </view>
    </view>

    <!-- 通知列表 -->
    <scroll-view class="notice-scroll" scroll-y @scrolltolower="loadMore" refresher-enabled
      @refresherrefresh="onRefresh" :refresher-triggered="refreshing">
      <view class="notice-list">
		 
        <view class="notice-item" v-for="notice in noticeList" :key="notice.noticeId"
          :class="{ unread: notice.isRead === '0' }" @click="readNotice(notice)">
          <view class="notice-header">
            <view class="notice-type" :class="getTypeClass(notice.noticeType)">
              {{ getTypeText(notice.noticeType) }}
            </view>
            <text class="notice-time">{{ formatTime(notice.publishTime) }}</text>
          </view>

          <view class="notice-title">{{ notice.noticeTitle }}</view>
          <view class="notice-content">{{ notice.noticeContent }}</view>

          <view class="notice-footer">
            <view class="read-status">
              <u-icon v-if="notice.isRead === '0'" name="chat" size="24" color="#ff6b35"></u-icon>
              <u-icon v-else name="checkmark-circle" size="24" color="#4caf50"></u-icon>
              <text class="status-text">{{ notice.isRead === '0' ? '未读' : '已读' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <u-loading-icon v-if="loading"></u-loading-icon>
        <text class="load-text">{{ loading ? '加载中...' : '上拉加载更多' }}</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" v-if="!hasMore && noticeList.length > 0">
        <text>没有更多通知了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && noticeList.length === 0">
        <text class="empty-text">暂无通知</text>
        <text class="empty-desc">您还没有收到任何通知消息</text>
      </view>
    </scroll-view>
	
    <!-- 底部操作栏 -->
    <view class="action-bar" v-if="noticeList.length > 0">
      <u-button size="small" @click="markAllAsRead">全部标记为已读</u-button>
      <u-button size="small" type="error" @click="clearAll">清空通知</u-button>
    </view>
  </view>
</template>

<script>
import { getNotices, markNoticeAsRead, markAllNoticesAsRead, clearAllNotices } from '@/api/home'
import { formatTime } from '@/utils/common'

export default {
  data() {
    return {
      currentFilter: 'all',
      noticeList: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      page: 1,
      pageSize: 20
    }
  },

  onLoad() {
    
  },

  onShow() {
	  this.loadNoticeList()
    // 从其他页面返回时刷新未读状态
    if (this.noticeList.length > 0) {
      this.refreshUnreadStatus()
    }
  },

  methods: {
    setFilter(filter) {
      this.currentFilter = filter
      this.page = 1
      this.hasMore = true
      this.noticeList = []
      this.loadNoticeList()
    },

    async loadNoticeList() {
      if (this.loading || !this.hasMore) return

      this.loading = true
      try {
        const params = {
          page: this.page,
          pageSize: this.pageSize
        }

        // 添加筛选条件
        if (this.currentFilter !== 'all') {
          params.noticeType = this.currentFilter
        }

        const response = await getNotices(params)

        if (response.code === 200) {
          const notices = response.data || []

          if (this.page === 1) {
            this.noticeList = notices
          } else {
            this.noticeList.push(...notices)
          }

          // 判断是否还有更多数据
          this.hasMore = notices.length === this.pageSize
          this.page++
        } else {
          uni.showToast({
            title: response.msg || '获取通知失败',
            icon: 'none'
          })
        }

      } catch (error) {
        console.error('加载通知列表失败:', error)
        uni.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    loadMore() {
      this.loadNoticeList()
    },

    onRefresh() {
      this.refreshing = true
      this.page = 1
      this.hasMore = true
      this.noticeList = []
      this.loadNoticeList()
    },

    async readNotice(notice) {
      // 跳转到通知详情页面
      uni.navigateTo({
        url: `/pages/notice/detail?notice=${encodeURIComponent(JSON.stringify(notice))}`
      })
    },

    refreshUnreadStatus() {
      // 刷新未读状态，实际应该从后端获取最新状态
      console.log('刷新未读状态')
    },

    async markAllAsRead() {
      uni.showModal({
        title: '确认操作',
        content: '确定要将所有通知标记为已读吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const response = await markAllNoticesAsRead()

              if (response.code === 200) {
                // 更新本地数据
                this.noticeList.forEach(notice => {
                  if (notice.isRead === '0') {
                    notice.isRead = '1'
                  }
                })

                uni.showToast({
                  title: '操作成功',
                  icon: 'success'
                })
              } else {
                uni.showToast({
                  title: response.msg || '操作失败',
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('标记已读失败:', error)
              uni.showToast({
                title: '网络错误，请稍后重试',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    async clearAll() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有通知吗？此操作不可恢复。',
        success: async (res) => {
          if (res.confirm) {
            try {
              const response = await clearAllNotices()

              if (response.code === 200) {
                this.noticeList = []
                uni.showToast({
                  title: '清空成功',
                  icon: 'success'
                })
              } else {
                uni.showToast({
                  title: response.msg || '清空失败',
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('清空通知失败:', error)
              uni.showToast({
                title: '网络错误，请稍后重试',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    getTypeText(type) {
      const typeMap = {
        '0': '系统',
        '1': '活动',
        '2': '任务'
      }
      return typeMap[type] || '通知'
    },

    getTypeClass(type) {
      const classMap = {
        '0': 'system',
        '1': 'activity',
        '2': 'task'
      }
      return classMap[type] || 'system'
    },

    formatTime
  }
}
</script>

<style lang="scss" scoped>
.notice-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f8f8;
}

.filter-bar {
  display: flex;
  background: #ffffff;
  padding: 20rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .filter-item {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    font-size: 28rpx;
    color: #666;
    border-radius: 40rpx;
    margin: 0 10rpx;

    &.active {
      background: #3cc51f;
      color: #ffffff;
    }
  }
}

.notice-scroll {
  flex: 1;
}

.notice-list {
  padding: 20rpx 40rpx;

  .notice-item {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    position: relative;

    &.unread {
      border-left: 8rpx solid #ff6b35;

      &::before {
        content: '';
        position: absolute;
        top: 30rpx;
        right: 30rpx;
        width: 16rpx;
        height: 16rpx;
        background: #ff6b35;
        border-radius: 50%;
      }
    }

    .notice-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .notice-type {
        padding: 8rpx 16rpx;
        border-radius: 16rpx;
        font-size: 22rpx;
        color: #ffffff;

        &.system {
          background: #2196f3;
        }

        &.activity {
          background: #ff9800;
        }

        &.task {
          background: #4caf50;
        }
      }

      .notice-time {
        font-size: 24rpx;
        color: #999;
      }
    }

    .notice-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 15rpx;
      line-height: 1.4;
    }

    .notice-content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
      margin-bottom: 20rpx;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
    }

    .notice-footer {
      display: flex;
      justify-content: flex-end;

      .read-status {
        display: flex;
        align-items: center;

        .status-text {
          margin-left: 8rpx;
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}

.load-more,
.no-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 32rpx;
    color: #666;
    margin-bottom: 20rpx;
    display: block;
  }

  .empty-desc {
    font-size: 26rpx;
    color: #999;
    display: block;
  }
}

.action-bar {
  background: #ffffff;
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;

  .u-button {
    flex: 1;
  }
}
</style>
