<template>
  <view class="notice-detail-container">
    <!-- 通知头部 -->
    <view class="notice-header">
      <view class="notice-type" :class="getTypeClass(notice.noticeType)">
        {{ getTypeText(notice.noticeType) }}
      </view>
      <view class="notice-time">{{ formatTime(notice.publishTime) }}</view>
    </view>
    
    <!-- 通知标题 -->
    <view class="notice-title">{{ notice.noticeTitle }}</view>
    
    <!-- 通知内容 -->
    <view class="notice-content">
      <rich-text :nodes="notice.noticeContent"></rich-text>
    </view>
    
    <!-- 通知状态 -->
    <view class="notice-status">
      <view class="status-item">
        <text class="status-label">发布时间：</text>
        <text class="status-value">{{ formatTime(notice.publishTime) }}</text>
      </view>
      <view class="status-item">
        <text class="status-label">阅读状态：</text>
        <text class="status-value" :class="{ unread: notice.isRead === '0' }">
          {{ notice.isRead === '0' ? '未读' : '已读' }}
        </text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <u-button 
        v-if="notice.isRead === '0'" 
        type="primary" 
        @click="markAsRead"
      >
        标记为已读
      </u-button>
      <u-button 
        type="default" 
        @click="goBack"
      >
        返回
      </u-button>
    </view>
  </view>
</template>

<script>
import { markNoticeAsRead } from '@/api/home'
import { formatTime } from '@/utils/common'

export default {
  data() {
    return {
      notice: {}
    }
  },
  
  onLoad(options) {
    if (options.notice) {
      this.notice = JSON.parse(decodeURIComponent(options.notice))
      
      // 如果是未读通知，自动标记为已读
      if (this.notice.isRead === '0') {
        this.markAsRead()
      }
    }
  },
  
  methods: {
    async markAsRead() {
      try {
        const response = await markNoticeAsRead(this.notice.noticeId)
        if (response.code === 200) {
          this.notice.isRead = '1'
        }
      } catch (error) {
        console.error('标记已读失败:', error)
      }
    },
    
    goBack() {
      uni.navigateBack()
    },
    
    getTypeText(type) {
      const typeMap = {
        '0': '系统通知',
        '1': '活动通知',
        '2': '任务通知'
      }
      return typeMap[type] || '通知'
    },
    
    getTypeClass(type) {
      const classMap = {
        '0': 'system',
        '1': 'activity',
        '2': 'task'
      }
      return classMap[type] || 'system'
    },
    
    formatTime
  }
}
</script>

<style lang="scss" scoped>
.notice-detail-container {
  padding: 40rpx;
  background: #f8f8f8;
  min-height: 100vh;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .notice-type {
    padding: 12rpx 24rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    color: #ffffff;
    
    &.system {
      background: #2196f3;
    }
    
    &.activity {
      background: #ff9800;
    }
    
    &.task {
      background: #4caf50;
    }
  }
  
  .notice-time {
    font-size: 26rpx;
    color: #999;
  }
}

.notice-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 30rpx;
  background: #ffffff;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.notice-content {
  background: #ffffff;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
  
  :deep(.rich-text) {
    font-size: 30rpx;
    line-height: 1.6;
    color: #666;
  }
}

.notice-status {
  background: #ffffff;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 40rpx;
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15rpx 0;
    
    &:not(:last-child) {
      border-bottom: 1rpx solid #f0f0f0;
    }
    
    .status-label {
      font-size: 28rpx;
      color: #666;
    }
    
    .status-value {
      font-size: 28rpx;
      color: #333;
      
      &.unread {
        color: #ff6b35;
        font-weight: bold;
      }
    }
  }
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  
  .u-button {
    flex: 1;
  }
}
</style>
