<template>
  <view class="feedback-container">
    <view class="feedback-header">
      <text class="header-title">帮助反馈</text>
      <text class="header-desc">遇到问题或有建议？我们很乐意听取您的意见</text>
    </view>

    <!-- 常见问题 -->
    <view class="faq-section">
      <view class="section-title">常见问题</view>
      <view class="faq-list">
        <view class="faq-item" v-for="(item, index) in faqList" :key="index" @click="toggleFaq(index)">
          <view class="faq-question">
            <text class="question-text">{{ item.question }}</text>
            <u-icon :name="item.expanded ? 'arrow-up' : 'arrow-down'" size="16" color="#999"></u-icon>
          </view>
          <view class="faq-answer" v-if="item.expanded">
            <text class="answer-text">{{ item.answer }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 反馈表单 -->
    <view class="feedback-form">
      <view class="section-title">意见反馈</view>
      
      <view class="form-group">
        <text class="form-label">反馈类型</text>
        <view class="type-selector">
          <view 
            class="type-item" 
            :class="{ active: feedbackType === item.value }"
            v-for="item in typeOptions" 
            :key="item.value"
            @click="selectType(item.value)"
          >
            <text class="type-text">{{ item.label }}</text>
          </view>
        </view>
      </view>

      <view class="form-group">
        <text class="form-label">问题描述 <text class="required">*</text></text>
        <textarea 
          class="feedback-textarea"
          v-model="feedbackContent"
          placeholder="请详细描述您遇到的问题或建议..."
          maxlength="500"
          :show-confirm-bar="false"
        ></textarea>
        <text class="char-count">{{ feedbackContent.length }}/500</text>
      </view>

      <view class="form-group">
        <text class="form-label">联系方式</text>
        <input 
          class="feedback-input"
          v-model="contactInfo"
          placeholder="请留下您的手机号或邮箱（选填）"
          maxlength="50"
        />
      </view>

      <view class="form-group">
        <text class="form-label">截图上传</text>
        <view class="image-upload">
          <view class="image-list">
            <view class="image-item" v-for="(image, index) in imageList" :key="index">
              <image class="uploaded-image" :src="image" mode="aspectFill" @click="previewImage(index)"></image>
              <view class="delete-btn" @click="deleteImage(index)">
                <u-icon name="close" size="12" color="#fff"></u-icon>
              </view>
            </view>
            <view class="upload-btn" v-if="imageList.length < 3" @click="chooseImage">
              <u-icon name="plus" size="24" color="#999"></u-icon>
              <text class="upload-text">添加图片</text>
            </view>
          </view>
          <text class="upload-tip">最多上传3张图片，支持jpg、png格式</text>
        </view>
      </view>

      <button class="submit-btn" :disabled="!canSubmit" @click="submitFeedback">
        <text class="submit-text">提交反馈</text>
      </button>
    </view>

    <!-- 联系方式 -->
    <view class="contact-section">
      <view class="section-title">其他联系方式</view>
      <view class="contact-list">
        <view class="contact-item" @click="callPhone">
          <u-icon name="phone" size="20" color="#007aff"></u-icon>
          <text class="contact-text">客服热线：************</text>
        </view>
        <view class="contact-item" @click="copyEmail">
          <u-icon name="email" size="20" color="#007aff"></u-icon>
          <text class="contact-text">邮箱：<EMAIL></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { chooseImage } from '@/utils/common'

export default {
  data() {
    return {
      feedbackType: 'bug',
      feedbackContent: '',
      contactInfo: '',
      imageList: [],
      
      typeOptions: [
        { label: '功能异常', value: 'bug' },
        { label: '功能建议', value: 'suggestion' },
        { label: '界面问题', value: 'ui' },
        { label: '其他问题', value: 'other' }
      ],
      
      faqList: [
        {
          question: '如何进行实名认证？',
          answer: '进入个人中心 > 实名认证，按照提示上传身份证照片并填写真实信息即可。',
          expanded: false
        },
        {
          question: '忘记密码怎么办？',
          answer: '在登录页面点击"忘记密码"，通过手机验证码重置密码。',
          expanded: false
        },
        {
          question: '如何提现佣金？',
          answer: '完成任务后，佣金会自动结算到账户余额，可在个人中心申请提现。',
          expanded: false
        },
        {
          question: '任务被拒绝怎么办？',
          answer: '可以查看拒绝原因，修改后重新提交，或联系客服处理。',
          expanded: false
        }
      ]
    }
  },

  computed: {
    canSubmit() {
      return this.feedbackContent.trim().length > 0
    }
  },

  methods: {
    toggleFaq(index) {
      this.faqList[index].expanded = !this.faqList[index].expanded
    },

    selectType(type) {
      this.feedbackType = type
    },

    async chooseImage() {
      try {
        const images = await chooseImage(3 - this.imageList.length)
        this.imageList.push(...images)
      } catch (error) {
        console.error('选择图片失败:', error)
      }
    },

    previewImage(index) {
      uni.previewImage({
        urls: this.imageList,
        current: index
      })
    },

    deleteImage(index) {
      this.imageList.splice(index, 1)
    },

    async submitFeedback() {
      if (!this.canSubmit) return

      uni.showLoading({
        title: '提交中...'
      })

      try {
        // 这里应该调用实际的API
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        uni.hideLoading()
        uni.showToast({
          title: '反馈提交成功',
          icon: 'success'
        })

        // 重置表单
        this.feedbackContent = ''
        this.contactInfo = ''
        this.imageList = []
        this.feedbackType = 'bug'

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        })
      }
    },

    callPhone() {
      uni.makePhoneCall({
        phoneNumber: '************'
      })
    },

    copyEmail() {
      uni.setClipboardData({
        data: '<EMAIL>',
        success: () => {
          uni.showToast({
            title: '邮箱已复制',
            icon: 'success'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.feedback-container {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 20rpx;
}

.feedback-header {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  text-align: center;

  .header-title {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 10rpx;
  }

  .header-desc {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
  }
}

.faq-section,
.feedback-form,
.contact-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.faq-list {
  .faq-item {
    border-bottom: 1rpx solid #f0f0f0;
    padding: 20rpx 0;

    &:last-child {
      border-bottom: none;
    }

    .faq-question {
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;

      .question-text {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }
    }

    .faq-answer {
      margin-top: 15rpx;
      padding-top: 15rpx;
      border-top: 1rpx solid #f8f8f8;

      .answer-text {
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
      }
    }
  }
}

.form-group {
  margin-bottom: 30rpx;

  .form-label {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 15rpx;

    .required {
      color: #ff4757;
    }
  }

  .type-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 15rpx;

    .type-item {
      padding: 15rpx 25rpx;
      border: 1rpx solid #e0e0e0;
      border-radius: 25rpx;
      background: #f8f8f8;

      &.active {
        background: #007aff;
        border-color: #007aff;

        .type-text {
          color: #ffffff;
        }
      }

      .type-text {
        font-size: 26rpx;
        color: #666;
      }
    }
  }

  .feedback-textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 20rpx;
    border: 1rpx solid #e0e0e0;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #333;
    background: #fafafa;
    box-sizing: border-box;
  }

  .feedback-input {
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    border: 1rpx solid #e0e0e0;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #333;
    background: #fafafa;
    box-sizing: border-box;
  }

  .char-count {
    display: block;
    text-align: right;
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
  }
}

.image-upload {
  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15rpx;
    margin-bottom: 15rpx;

    .image-item {
      position: relative;
      width: 150rpx;
      height: 150rpx;

      .uploaded-image {
        width: 100%;
        height: 100%;
        border-radius: 12rpx;
        background: #f0f0f0;
      }

      .delete-btn {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        width: 40rpx;
        height: 40rpx;
        background: #ff4757;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .upload-btn {
      width: 150rpx;
      height: 150rpx;
      border: 2rpx dashed #e0e0e0;
      border-radius: 12rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #fafafa;

      .upload-text {
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
      }
    }
  }

  .upload-tip {
    font-size: 24rpx;
    color: #999;
  }
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #007aff;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;

  &:disabled {
    background: #cccccc;
  }

  .submit-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
  }
}

.contact-list {
  .contact-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background: #f8f9fa;
      border-radius: 8rpx;
    }

    .contact-text {
      margin-left: 15rpx;
      font-size: 28rpx;
      color: #333;
    }
  }
}
</style>
