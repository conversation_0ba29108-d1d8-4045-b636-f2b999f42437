<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="bg-circle circle-1"></view>
      <view class="bg-circle circle-2"></view>
      <view class="bg-circle circle-3"></view>
    </view>

    <!-- 顶部装饰波浪 -->
    <view class="wave-decoration">
      <view class="wave wave-1"></view>
      <view class="wave wave-2"></view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-wrapper">
      <!-- 头部信息 -->
      <view class="header">
        <view class="logo-container">
          <image class="logo" src="/static/logo.svg" mode="aspectFit"></image>
          <view class="logo-glow"></view>
        </view>
        <text class="subtitle">未注册的手机号登录后会自动创建账号</text>
        <view class="title-underline"></view>
      </view>

      <!-- 登录表单 -->
      <view class="form-container">
        <!-- 登录方式切换 -->
        <view class="login-type-tabs">
          <view class="tab-item" :class="{ active: loginType === 'password' }" @click="switchLoginType('password')">
            <text class="tab-text">密码登录</text>
          </view>
          <view class="tab-item" :class="{ active: loginType === 'sms' }" @click="switchLoginType('sms')">
            <text class="tab-text">验证码登录</text>
          </view>
        </view>

        <!-- 手机号输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <u-icon name="phone" size="20" color="#666"></u-icon>
            </view>
            <u-input v-model="form.phone" placeholder="请输入手机号" :border="false" :clearable="true" class="custom-input"
              type="number" maxlength="11" />
          </view>
        </view>

        <!-- 密码登录 -->
        <view v-if="loginType === 'password'" class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <u-icon name="lock" size="20" color="#666"></u-icon>
            </view>
            <u-input v-model="form.password" type="password" placeholder="请输入密码" :border="false" :clearable="true"
              class="custom-input" />
          </view>
        </view>

        <!-- 验证码登录 -->
        <view v-if="loginType === 'sms'" class="input-group">
          <view class="input-wrapper sms-wrapper">
            <view class="input-icon">
              <u-icon name="checkmark" size="20" color="#666"></u-icon>
            </view>
            <u-input v-model="form.smsCode" placeholder="请输入验证码" :border="false" :clearable="true"
              class="custom-input sms-input" type="number" maxlength="6" />
            <view class="sms-btn" @click="sendSmsCode" :class="{ disabled: smsCountdown > 0 }">
              <text class="sms-btn-text">
                {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
              </text>
            </view>
          </view>
        </view>

        <!-- 忘记密码 -->
        <view v-if="loginType === 'password'" class="forgot-password">
          <text class="link" @click="forgotPassword">忘记密码？</text>
        </view>

        <!-- 协议同意 -->
        <view class="agreement-row">
          <u-checkbox v-model="agreed" :size="20" active-color="#667eea" @change="onAgreementChange">
            <text class="agreement-text">我已阅读并同意</text>
          </u-checkbox>
          <text class="link" @click="showAgreement('user')">《服务协议》</text>
          <text class="agreement-text">和</text>
          <text class="link" @click="showAgreement('privacy')">《隐私协议》</text>
        </view>

        <!-- 登录按钮 -->
        <view class="btn-group">
          <u-button :loading="loading" :disabled="!canLogin" @click="handleLogin" class="login-btn"
            :custom-style="loginBtnStyle">
            <text class="btn-text">登录</text>
          </u-button>
        </view>

        <!-- 分割线 -->
        <view class="divider">
          <view class="divider-line"></view>
          <text class="divider-text">或</text>
          <view class="divider-line"></view>
        </view>

        <!-- 游客模式 -->
        <view class="guest-mode">
          <text class="guest-text" @click="guestMode">游客模式，继续使用</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { login, smsLogin, sendSmsCode, getUserAgreement, getPrivacyPolicy } from '@/api/auth'

export default {
  data() {
    return {
      loginType: 'password', // 'password' | 'sms'
      form: {
        phone: '',
        password: '',
        smsCode: ''
      },
      agreed: false,
      loading: false,
      smsCountdown: 0,
      smsTimer: null,
      userAgreement: '',
      privacyPolicy: ''
    }
  },

  computed: {
    canLogin() {
      if (this.loginType === 'password') {
        return this.form.phone && this.form.password && this.agreed && !this.loading
      } else {
        return this.form.phone && this.form.smsCode && this.agreed && !this.loading
      }
    },

    loginBtnStyle() {
      return {
        background: this.canLogin
          ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
          : 'linear-gradient(135deg, #ccc 0%, #999 100%)',
        border: 'none',
        borderRadius: '25px',
        height: '50px',
        boxShadow: this.canLogin ? '0 8px 20px rgba(102, 126, 234, 0.3)' : 'none'
      }
    }
  },

  async mounted() {
    // 获取协议内容
    await this.loadAgreements()
  },

  beforeDestroy() {
    if (this.smsTimer) {
      clearInterval(this.smsTimer)
    }
  },

  methods: {
    // 切换登录方式
    switchLoginType(type) {
      this.loginType = type
      // 清空表单
      this.form.password = ''
      this.form.smsCode = ''
    },

    // 加载协议内容
    async loadAgreements() {
      try {
        const [userRes, privacyRes] = await Promise.all([
          getUserAgreement(),
          getPrivacyPolicy()
        ])
        this.userAgreement = userRes.data || ''
        this.privacyPolicy = privacyRes.data || ''
      } catch (error) {
        console.error('获取协议内容失败:', error)
      }
    },

    // 协议同意状态变化
    onAgreementChange(value) {
      if (value) {
        this.showPrivacyModal()
      }
    },

    // 显示隐私保护弹框
    showPrivacyModal() {
      uni.showModal({
        title: '隐私保护提示',
        content: '感谢您使用浮光APP，我们非常重视您的个人信息和隐私保护。为了更好的保障您的个人权益，您在使用我们产品前，希望您确认以下关于用户权限的说明：\n\n1、我们会根据您使用服务的具体功能需要，收集用户信息（涉及设备信息、账户等），并且严格遵循隐私政策收集、使用这些信息。\n\n2、您可以在"我的"页面或相应的服务页管理您的个人信息。\n\n3、我们的信息只会存储在您设备的本地以及我们在中国大陆境内的服务器中。\n\n4、我们会尽全力保护您的信息安全。',
        confirmText: '同意并继续',
        success: (res) => {
          if (res.confirm) {
            // 用户同意，保持agreed为true
            this.agreed = true
          } else {
            // 用户不同意，取消勾选并进入游客模式
            this.agreed = false
            this.guestMode()
          }
        }
      })
    },

    // 发送验证码
    async sendSmsCode() {
      if (this.smsCountdown > 0) return

      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      // 验证手机号格式
      const phoneReg = /^1[3-9]\d{9}$/
      if (!phoneReg.test(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      try {
        await sendSmsCode({ phone: this.form.phone })
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })

        // 开始倒计时
        this.startCountdown()
      } catch (error) {
        console.error('发送验证码失败:', error)
      }
    },

    // 开始倒计时
    startCountdown() {
      this.smsCountdown = 60
      this.smsTimer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer)
          this.smsTimer = null
        }
      }, 1000)
    },

    async handleLogin() {
      if (!this.canLogin) return

      this.loading = true
      try {
        let res
        if (this.loginType === 'password') {
          res = await login({
            username: this.form.phone,
            password: this.form.password
          })
        } else {
          res = await smsLogin({
            username: this.form.phone,
            smsCode: this.form.smsCode
          })
        }

        // 保存token和用户信息
        uni.setStorageSync('token', res.token)
        if (res.user) {
          uni.setStorageSync('userInfo', res.user)
          // 同时更新 Vuex store
          this.$store.dispatch('login', res.user)
        }
        uni.removeStorageSync('isGuest') // 清除游客标识

        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 跳转到首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index'
          })
        }, 1500)

      } catch (error) {
        console.error('登录失败:', error)
      } finally {
        this.loading = false
      }
    },

    guestMode() {
      uni.showModal({
        title: '游客模式',
        content: '游客模式下功能受限，建议注册登录获得完整体验',
        confirmText: '继续',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 设置游客标识
            uni.setStorageSync('isGuest', true)
            uni.removeStorageSync('token') // 清除token
            uni.switchTab({
              url: '/pages/index/index'
            })
          }
        }
      })
    },

    showAgreement(type) {
      const content = type === 'user' ? this.userAgreement : this.privacyPolicy
      const title = type === 'user' ? '服务协议' : '隐私协议'

      if (!content) {
        uni.showToast({
          title: '协议内容加载中...',
          icon: 'none'
        })
        return
      }

      uni.showModal({
        title: title,
        content: content,
        showCancel: false,
        confirmText: '已知悉'
      })
    },

    forgotPassword() {
      uni.showModal({
        title: '忘记密码',
        content: '请联系客服或通过注册手机号找回密码',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

// 背景装饰
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;

  .bg-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);

    &.circle-1 {
      width: 200rpx;
      height: 200rpx;
      top: 10%;
      right: -50rpx;
      animation: float 6s ease-in-out infinite;
    }

    &.circle-2 {
      width: 150rpx;
      height: 150rpx;
      top: 60%;
      left: -30rpx;
      animation: float 8s ease-in-out infinite reverse;
    }

    &.circle-3 {
      width: 100rpx;
      height: 100rpx;
      top: 30%;
      left: 20%;
      animation: float 10s ease-in-out infinite;
    }
  }
}

// 波浪装饰
.wave-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  z-index: 2;

  .wave {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);

    &.wave-1 {
      clip-path: polygon(0 0, 100% 0, 100% 60%, 0 80%);
      animation: wave 8s ease-in-out infinite;
    }

    &.wave-2 {
      clip-path: polygon(0 0, 100% 0, 100% 40%, 0 60%);
      animation: wave 10s ease-in-out infinite reverse;
      opacity: 0.5;
    }
  }
}

// 主要内容
.content-wrapper {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  padding: 80rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

// 头部
.header {
  text-align: center;
  margin-bottom: 80rpx;

  .logo-container {
    position: relative;
    display: inline-block;
    margin-bottom: 30rpx;

    .logo {
      width: 140rpx;
      height: 140rpx;
      border-radius: 50%;
      box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
    }

    .logo-glow {
      position: absolute;
      top: -10rpx;
      left: -10rpx;
      right: -10rpx;
      bottom: -10rpx;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
      animation: glow 3s ease-in-out infinite alternate;
    }
  }

  .title {
    display: block;
    font-size: 52rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 15rpx;
    text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
  }

  .subtitle {
    display: block;
    font-size: 30rpx;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20rpx;
  }

  .title-underline {
    width: 80rpx;
    height: 4rpx;
    background: linear-gradient(90deg, transparent, #ffffff, transparent);
    margin: 0 auto;
    border-radius: 2rpx;
  }
}

// 表单容器
.form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

// 登录方式切换
.login-type-tabs {
  display: flex;
  background: #f5f5f5;
  border-radius: 15rpx;
  padding: 6rpx;
  margin-bottom: 40rpx;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    border-radius: 10rpx;
    transition: all 0.3s ease;

    &.active {
      background: #ffffff;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

      .tab-text {
        color: #667eea;
        font-weight: bold;
      }
    }

    .tab-text {
      font-size: 28rpx;
      color: #666;
    }
  }
}

// 输入框组
.input-group {
  margin-bottom: 30rpx;

  .input-wrapper {
    position: relative;
    background: #f8f9fa;
    border-radius: 15rpx;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #667eea;
      box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
    }

    &.sms-wrapper {
      display: flex;
      align-items: center;
    }

    .input-icon {
      position: absolute;
      left: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      z-index: 2;
    }

    .custom-input {
      padding-left: 60rpx !important;
      height: 90rpx;
      background: transparent;

      &.sms-input {
        flex: 1;
        padding-right: 140rpx !important;
      }

      :deep(.u-input__content) {
        height: 90rpx;

        .u-input__content__field-wrapper {
          height: 90rpx;

          .u-input__content__field-wrapper__field {
            height: 90rpx;
            line-height: 90rpx;
            font-size: 30rpx;
          }
        }
      }
    }

    .sms-btn {
      position: absolute;
      right: 10rpx;
      top: 50%;
      transform: translateY(-50%);
      background: #667eea;
      color: #ffffff;
      padding: 15rpx 20rpx;
      border-radius: 10rpx;
      font-size: 24rpx;
      transition: all 0.3s ease;

      &.disabled {
        background: #ccc;
        color: #999;
      }

      .sms-btn-text {
        color: inherit;
        font-size: 24rpx;
      }
    }
  }
}

// 忘记密码
.forgot-password {
  text-align: right;
  margin-bottom: 30rpx;

  .link {
    font-size: 26rpx;
    color: #667eea;
  }
}

// 协议同意
.agreement-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  font-size: 24rpx;
  flex-wrap: wrap;

  .agreement-text {
    color: #666;
    margin: 0 4rpx;
  }

  .link {
    color: #667eea;
    margin: 0 4rpx;
  }
}

// 按钮组
.btn-group {
  margin-bottom: 40rpx;

  .login-btn {
    width: 100%;
    height: 100rpx;
    border-radius: 50rpx;
    font-size: 32rpx;
    font-weight: bold;
    border: none;
    position: relative;
    overflow: hidden;

    .btn-text {
      color: #ffffff;
      font-weight: bold;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:active::before {
      left: 100%;
    }
  }
}

// 分割线
.divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;

  .divider-line {
    flex: 1;
    height: 1rpx;
    background: #e0e0e0;
  }

  .divider-text {
    margin: 0 20rpx;
    font-size: 24rpx;
    color: #999;
  }
}

// 游客模式
.guest-mode {
  text-align: center;
  margin-top: 30rpx;

  .guest-text {
    font-size: 28rpx;
    color: #667eea;
    text-decoration: underline;
  }
}

// 动画
@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }
}

@keyframes wave {

  0%,
  100% {
    transform: translateX(0px);
  }

  50% {
    transform: translateX(10px);
  }
}

@keyframes glow {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }

  100% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .content-wrapper {
    padding: 60rpx 30rpx 30rpx;
  }

  .form-container {
    padding: 50rpx 30rpx;
  }

  .header .title {
    font-size: 48rpx;
  }
}
</style>
