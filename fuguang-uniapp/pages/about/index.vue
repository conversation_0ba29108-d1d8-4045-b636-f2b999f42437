<template>
  <view class="about-container">
    <view class="about-header">
      <image class="app-logo" src="/static/logo.svg" mode="aspectFit"></image>
      <text class="app-name">浮光壁垒</text>
      <text class="app-version">版本 v1.0.0</text>
    </view>

    <view class="about-content">
      <view class="section">
        <view class="section-title">应用介绍</view>
        <text class="section-text">浮光壁垒是一款专业的任务管理平台，致力于为用户提供高效、安全、便捷的任务发布与接取服务。通过实名认证和信用体系，构建可信赖的任务生态圈。</text>
      </view>

      <view class="section">
        <view class="section-title">核心功能</view>
        <view class="feature-list">
          <view class="feature-item">
            <u-icon name="checkmark-circle" size="16" color="#4caf50"></u-icon>
            <text class="feature-text">任务发布与管理</text>
          </view>
          <view class="feature-item">
            <u-icon name="checkmark-circle" size="16" color="#4caf50"></u-icon>
            <text class="feature-text">实名认证体系</text>
          </view>
          <view class="feature-item">
            <u-icon name="checkmark-circle" size="16" color="#4caf50"></u-icon>
            <text class="feature-text">信用评分机制</text>
          </view>
          <view class="feature-item">
            <u-icon name="checkmark-circle" size="16" color="#4caf50"></u-icon>
            <text class="feature-text">安全保障金</text>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">联系我们</view>
        <view class="contact-list">
          <view class="contact-item" @click="copyText('************')">
            <u-icon name="phone" size="16" color="#666"></u-icon>
            <text class="contact-text">客服热线：************</text>
          </view>
          <view class="contact-item" @click="copyText('<EMAIL>')">
            <u-icon name="email" size="16" color="#666"></u-icon>
            <text class="contact-text">邮箱：<EMAIL></text>
          </view>
          <view class="contact-item">
            <u-icon name="map" size="16" color="#666"></u-icon>
            <text class="contact-text">地址：北京市朝阳区科技园区</text>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">法律信息</view>
        <view class="legal-links">
          <text class="link-text" @click="goUserAgreement">用户协议</text>
          <text class="link-text" @click="goPrivacy">隐私政策</text>
        </view>
      </view>
    </view>

    <view class="about-footer">
      <text class="copyright">© 2025 浮光壁垒 版权所有</text>
      <text class="company">北京浮光科技有限公司</text>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    },

    goUserAgreement() {
      uni.navigateTo({
        url: '/pages/agreement/user'
      })
    },

    goPrivacy() {
      uni.navigateTo({
        url: '/pages/agreement/privacy'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.about-container {
  min-height: 100vh;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

.about-header {
  background: #ffffff;
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;

  .app-logo {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 20rpx;
  }

  .app-name {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 10rpx;
  }

  .app-version {
    display: block;
    font-size: 26rpx;
    color: #666;
  }
}

.about-content {
  flex: 1;
  padding: 20rpx;

  .section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 20rpx;
    }

    .section-text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
    }
  }

  .feature-list {
    .feature-item {
      display: flex;
      align-items: center;
      margin-bottom: 15rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .feature-text {
        margin-left: 15rpx;
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .contact-list {
    .contact-item {
      display: flex;
      align-items: center;
      margin-bottom: 15rpx;
      padding: 10rpx 0;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background: #f8f9fa;
        border-radius: 8rpx;
      }

      .contact-text {
        margin-left: 15rpx;
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .legal-links {
    display: flex;
    gap: 30rpx;

    .link-text {
      font-size: 28rpx;
      color: #007aff;
      text-decoration: underline;

      &:active {
        opacity: 0.7;
      }
    }
  }
}

.about-footer {
  background: #ffffff;
  padding: 30rpx;
  text-align: center;
  border-top: 1rpx solid #f0f0f0;

  .copyright {
    display: block;
    font-size: 24rpx;
    color: #999;
    margin-bottom: 8rpx;
  }

  .company {
    display: block;
    font-size: 24rpx;
    color: #999;
  }
}
</style>
