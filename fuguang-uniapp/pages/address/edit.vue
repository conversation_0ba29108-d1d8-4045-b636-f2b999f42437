<template>
  <view class="address-edit-container">
    <view class="form-container">
      <!-- 联系人信息 -->
      <view class="form-section">
        <view class="section-title">联系人信息</view>
        
        <view class="form-item">
          <text class="label">联系人姓名 <text class="required">*</text></text>
          <input 
            class="input" 
            v-model="form.contactName" 
            placeholder="请输入联系人姓名"
            maxlength="20"
          />
        </view>
        
        <view class="form-item">
          <text class="label">联系人手机号 <text class="required">*</text></text>
          <input 
            class="input" 
            v-model="form.contactPhone" 
            placeholder="请输入联系人手机号"
            type="number"
            maxlength="11"
          />
        </view>
        
        <view class="form-item">
          <text class="label">联系人性别</text>
          <view class="radio-group">
            <label class="radio-item" v-for="item in sexOptions" :key="item.value">
              <radio 
                :value="item.value" 
                :checked="form.contactSex === item.value"
                @change="onSexChange"
                color="#007AFF"
              />
              <text class="radio-text">{{ item.label }}</text>
            </label>
          </view>
        </view>
      </view>
      
      <!-- 地址信息 -->
      <view class="form-section">
        <view class="section-title">地址信息</view>
        
        <view class="form-item location-item" @click="chooseLocation">
          <text class="label">所在地区 <text class="required">*</text></text>
          <view class="location-display">
            <text class="location-text" v-if="locationText">{{ locationText }}</text>
            <text class="placeholder" v-else>请选择所在地区</text>
            <u-icon name="arrow-right" size="16" color="#CCCCCC"></u-icon>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">详细地址 <text class="required">*</text></text>
          <textarea 
            class="textarea" 
            v-model="form.address" 
            placeholder="请输入详细地址，如街道、门牌号等"
            maxlength="200"
            auto-height
          />
        </view>
      </view>
      
      <!-- 设置选项 -->
      <view class="form-section">
        <view class="form-item switch-item">
          <text class="label">设为默认地址</text>
          <switch 
            :checked="form.isDefault === '1'" 
            @change="onDefaultChange"
            color="#007AFF"
          />
        </view>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="save-btn" @click="saveAddress">
      <text>保存地址</text>
    </view>
  </view>
</template>

<script>
import { getAddressDetail, addAddress, updateAddress } from '@/api/address'
import { chooseLocation, getCurrentLocation } from '@/api/address'

export default {
  data() {
    return {
      addressId: null,
      isEdit: false,
      form: {
        contactName: '',
        contactPhone: '',
        contactSex: '0',
        province: '',
        city: '',
        district: '',
        address: '',
        longitude: '',
        latitude: '',
        isDefault: '0'
      },
      sexOptions: [
        { label: '男', value: '0' },
        { label: '女', value: '1' },
        { label: '未知', value: '2' }
      ]
    }
  },
  
  computed: {
    locationText() {
      let text = ''
      if (this.form.province) text += this.form.province
      if (this.form.city) text += this.form.city
      if (this.form.district) text += this.form.district
      return text
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.addressId = options.id
      this.isEdit = true
      uni.setNavigationBarTitle({
        title: '编辑地址'
      })
      this.loadAddressDetail()
    } else {
      uni.setNavigationBarTitle({
        title: '新增地址'
      })
    }
  },
  
  methods: {
    // 加载地址详情
    async loadAddressDetail() {
      try {
        const res = await getAddressDetail(this.addressId)
        if (res.code === 200) {
          this.form = {
            ...this.form,
            ...res.data
          }
        } else {
          uni.showToast({
            title: res.msg || '获取地址详情失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取地址详情失败:', error)
        uni.showToast({
          title: '获取地址详情失败',
          icon: 'none'
        })
      }
    },
    
    // 性别选择
    onSexChange(e) {
      this.form.contactSex = e.detail.value
    },
    
    // 默认地址切换
    onDefaultChange(e) {
      this.form.isDefault = e.detail.value ? '1' : '0'
    },
    
    // 选择位置
    async chooseLocation() {
      try {
        const location = await chooseLocation()
        
        // 解析地址信息
        const address = location.address || ''
        const addressParts = this.parseAddress(address)
        
        this.form.province = addressParts.province
        this.form.city = addressParts.city
        this.form.district = addressParts.district
        this.form.longitude = location.longitude.toString()
        this.form.latitude = location.latitude.toString()
        
        // 如果有具体地址名称，设置为详细地址
        if (location.name && location.name !== address) {
          this.form.address = location.name
        }
        
      } catch (error) {
        if (error.type !== 'cancel') {
          console.error('选择位置失败:', error)
        }
      }
    },
    
    // 解析地址字符串
    parseAddress(address) {
      // 简单的地址解析，实际项目中可能需要更复杂的解析逻辑
      const result = {
        province: '',
        city: '',
        district: ''
      }
      
      // 这里可以根据实际需求实现地址解析逻辑
      // 例如通过正则表达式或第三方地址解析服务
      
      return result
    },
    
    // 表单验证
    validateForm() {
      if (!this.form.contactName.trim()) {
        uni.showToast({
          title: '请输入联系人姓名',
          icon: 'none'
        })
        return false
      }
      
      if (!this.form.contactPhone.trim()) {
        uni.showToast({
          title: '请输入联系人手机号',
          icon: 'none'
        })
        return false
      }
      
      if (!/^1[3-9]\d{9}$/.test(this.form.contactPhone)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        })
        return false
      }
      
      if (!this.form.address.trim()) {
        uni.showToast({
          title: '请输入详细地址',
          icon: 'none'
        })
        return false
      }
      
      return true
    },
    
    // 保存地址
    async saveAddress() {
      if (!this.validateForm()) {
        return
      }
      
      try {
        uni.showLoading({
          title: '保存中...'
        })
        
        const data = { ...this.form }
        let res
        
        if (this.isEdit) {
          data.addressId = this.addressId
          res = await updateAddress(data)
        } else {
          res = await addAddress(data)
        }
        
        uni.hideLoading()
        
        if (res.code === 200) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
          
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: res.msg || '保存失败',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('保存地址失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.address-edit-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.form-container {
  padding: 20rpx;
}

.form-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
}

.required {
  color: #FF3B30;
}

.input, .textarea {
  width: 100%;
  font-size: 28rpx;
  color: #333333;
  
  &::placeholder {
    color: #CCCCCC;
  }
}

.textarea {
  min-height: 80rpx;
  line-height: 1.5;
}

.radio-group {
  display: flex;
  gap: 40rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.radio-text {
  font-size: 28rpx;
  color: #333333;
}

.location-item {
  cursor: pointer;
}

.location-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.location-text {
  font-size: 28rpx;
  color: #333333;
}

.placeholder {
  font-size: 28rpx;
  color: #CCCCCC;
}

.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.save-btn {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  height: 88rpx;
  background-color: #007AFF;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
}
</style>
