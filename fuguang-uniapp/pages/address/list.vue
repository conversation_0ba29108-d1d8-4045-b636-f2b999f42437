<template>
  <view class="address-list-container">
    <!-- 地址列表 -->
    <view class="address-list" v-if="addressList.length > 0">
      <view 
        class="address-item" 
        v-for="address in addressList" 
        :key="address.addressId"
        @click="selectAddress(address)"
      >
        <!-- 默认标签 -->
        <view class="default-tag" v-if="address.isDefault === '1'">默认</view>
        
        <!-- 地址信息 -->
        <view class="address-info">
          <view class="contact-info">
            <text class="contact-name">{{ address.contactName }}</text>
            <text class="contact-phone">{{ address.contactPhone }}</text>
          </view>
          <view class="address-detail">
            <text class="full-address">{{ address.fullAddress || getFullAddress(address) }}</text>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="address-actions">
          <view class="action-btn edit-btn" @click.stop="editAddress(address)">
            <u-icon name="edit-pen" size="16" color="#007AFF"></u-icon>
            <text>编辑</text>
          </view>
          <view class="action-btn delete-btn" @click.stop="deleteAddress(address)">
            <u-icon name="trash" size="16" color="#FF3B30"></u-icon>
            <text>删除</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image class="empty-icon" src="/static/empty-address.png" mode="aspectFit"></image>
      <text class="empty-text">暂无地址信息</text>
      <text class="empty-tip">点击下方按钮添加新地址</text>
    </view>
    
    <!-- 添加地址按钮 -->
    <view class="add-address-btn" @click="addAddress">
      <u-icon name="plus" size="20" color="#FFFFFF"></u-icon>
      <text>新增地址</text>
    </view>
  </view>
</template>

<script>
import { getAddressList, deleteAddress as deleteAddressApi, setDefaultAddress } from '@/api/address'

export default {
  data() {
    return {
      addressList: [],
      loading: false,
      selectMode: false, // 是否为选择模式
    }
  },
  
  onLoad(options) {
    // 检查是否为选择模式
    if (options.select === 'true') {
      this.selectMode = true
      uni.setNavigationBarTitle({
        title: '选择地址'
      })
    }
    this.loadAddressList()
  },
  
  onShow() {
    // 从编辑页面返回时刷新列表
    this.loadAddressList()
  },
  
  methods: {
    // 加载地址列表
    async loadAddressList() {
      try {
        this.loading = true
        const res = await getAddressList()
        if (res.code === 200) {
          this.addressList = res.data || []
        } else {
          uni.showToast({
            title: res.msg || '获取地址列表失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取地址列表失败:', error)
        uni.showToast({
          title: '获取地址列表失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 选择地址（选择模式下）
    selectAddress(address) {
      if (this.selectMode) {
        // 返回选中的地址
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2]
        if (prevPage) {
          prevPage.$vm.onAddressSelected && prevPage.$vm.onAddressSelected(address)
        }
        uni.navigateBack()
      } else {
        // 非选择模式下，点击设置为默认地址
        this.setDefault(address)
      }
    },
    
    // 设置默认地址
    async setDefault(address) {
      if (address.isDefault === '1') {
        return
      }
      
      try {
        const res = await setDefaultAddress(address.addressId)
        if (res.code === 200) {
          uni.showToast({
            title: '设置成功',
            icon: 'success'
          })
          this.loadAddressList()
        } else {
          uni.showToast({
            title: res.msg || '设置失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('设置默认地址失败:', error)
        uni.showToast({
          title: '设置失败',
          icon: 'none'
        })
      }
    },
    
    // 编辑地址
    editAddress(address) {
      uni.navigateTo({
        url: `/pages/address/edit?id=${address.addressId}`
      })
    },
    
    // 删除地址
    deleteAddress(address) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个地址吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await deleteAddressApi(address.addressId)
              if (result.code === 200) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                })
                this.loadAddressList()
              } else {
                uni.showToast({
                  title: result.msg || '删除失败',
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('删除地址失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 添加地址
    addAddress() {
      uni.navigateTo({
        url: '/pages/address/edit'
      })
    },
    
    // 获取完整地址
    getFullAddress(address) {
      let fullAddress = ''
      if (address.province) fullAddress += address.province
      if (address.city) fullAddress += address.city
      if (address.district) fullAddress += address.district
      if (address.address) fullAddress += address.address
      return fullAddress
    }
  }
}
</script>

<style lang="scss" scoped>
.address-list-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 100rpx;
}

.address-list {
  padding: 20rpx;
}

.address-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.default-tag {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #007AFF;
  color: #ffffff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 0 16rpx 0 16rpx;
}

.address-info {
  margin-bottom: 20rpx;
}

.contact-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.contact-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 20rpx;
}

.contact-phone {
  font-size: 28rpx;
  color: #666666;
}

.address-detail {
  .full-address {
    font-size: 28rpx;
    color: #666666;
    line-height: 1.5;
  }
}

.address-actions {
  display: flex;
  justify-content: flex-end;
  gap: 30rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.edit-btn {
  color: #007AFF;
  background-color: rgba(0, 122, 255, 0.1);
}

.delete-btn {
  color: #FF3B30;
  background-color: rgba(255, 59, 48, 0.1);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 16rpx;
}

.empty-tip {
  font-size: 28rpx;
  color: #CCCCCC;
}

.add-address-btn {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  height: 88rpx;
  background-color: #007AFF;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
}
</style>
