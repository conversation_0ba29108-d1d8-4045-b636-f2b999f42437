<template>
  <view class="forget-password-container">
    <view class="header">
      <text class="title">忘记密码</text>
      <text class="subtitle">通过手机验证码重置密码</text>
    </view>

    <view class="form-container">
      <view class="form-group">
        <text class="label">手机号</text>
        <view class="input-wrapper">
          <input class="input" v-model="phone" placeholder="请输入手机号" type="number" maxlength="11" />
        </view>
      </view>

      <view class="form-group">
        <text class="label">验证码</text>
        <view class="input-wrapper code-wrapper">
          <input class="input code-input" v-model="code" placeholder="请输入验证码" type="number" maxlength="6" />
          <button class="code-btn" :disabled="!canSendCode || countdown > 0" @click="sendCode">
            <text class="code-btn-text">
              {{ countdown > 0 ? `${countdown}s后重发` : '获取验证码' }}
            </text>
          </button>
        </view>
      </view>

      <view class="form-group">
        <text class="label">新密码</text>
        <view class="input-wrapper">
          <input class="input" v-model="newPassword" placeholder="请输入新密码" :password="!showPassword" maxlength="20" />
          <view class="eye-btn" @click="togglePassword">
            <u-icon :name="showPassword ? 'eye' : 'eye-off'" size="20" color="#999"></u-icon>
          </view>
        </view>
      </view>

      <view class="form-group">
        <text class="label">确认密码</text>
        <view class="input-wrapper">
          <input class="input" v-model="confirmPassword" placeholder="请再次输入新密码" :password="!showConfirmPassword"
            maxlength="20" />
          <view class="eye-btn" @click="toggleConfirmPassword">
            <u-icon :name="showConfirmPassword ? 'eye' : 'eye-off'" size="20" color="#999"></u-icon>
          </view>
        </view>
      </view>

      <button class="submit-btn" :disabled="!canSubmit" @click="resetPassword">
        <text class="submit-text">重置密码</text>
      </button>

      <view class="tips">
        <text class="tip-text">• 密码长度为6-20位</text>
        <text class="tip-text">• 建议包含字母、数字和特殊字符</text>
        <text class="tip-text">• 重置成功后请妥善保管新密码</text>
      </view>
    </view>
  </view>
</template>

<script>
import { sendSmsCode, resetPassword } from '@/api/auth'
import { validatePhone } from '@/utils/common'

export default {
  data() {
    return {
      phone: '',
      code: '',
      newPassword: '',
      confirmPassword: '',
      showPassword: false,
      showConfirmPassword: false,
      countdown: 0,
      timer: null
    }
  },

  computed: {
    canSendCode() {
      return validatePhone(this.phone)
    },

    canSubmit() {
      return this.phone &&
        this.code &&
        this.newPassword &&
        this.confirmPassword &&
        this.newPassword === this.confirmPassword &&
        this.newPassword.length >= 6
    }
  },

  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },

  methods: {
    async sendCode() {
      if (!this.canSendCode) return

      try {
        await sendSmsCode({
          phone: this.phone,
          type: 'reset'
        })

        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })

        this.startCountdown()

      } catch (error) {
        console.error('发送验证码失败:', error)
        uni.showToast({
          title: error.message || '发送失败',
          icon: 'none'
        })
      }
    },

    startCountdown() {
      this.countdown = 60
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.timer)
          this.timer = null
        }
      }, 1000)
    },

    togglePassword() {
      this.showPassword = !this.showPassword
    },

    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword
    },

    async resetPassword() {
      if (!this.canSubmit) return

      if (this.newPassword !== this.confirmPassword) {
        uni.showToast({
          title: '两次密码输入不一致',
          icon: 'none'
        })
        return
      }

      if (this.newPassword.length < 6) {
        uni.showToast({
          title: '密码长度不能少于6位',
          icon: 'none'
        })
        return
      }

      uni.showLoading({
        title: '重置中...'
      })

      try {
        await resetPassword({
          phone: this.phone,
          code: this.code,
          newPassword: this.newPassword
        })

        uni.hideLoading()
        uni.showModal({
          title: '重置成功',
          content: '密码重置成功，请使用新密码登录',
          showCancel: false,
          success: () => {
            uni.navigateBack()
          }
        })

      } catch (error) {
        uni.hideLoading()
        console.error('重置密码失败:', error)
        uni.showToast({
          title: error.message || '重置失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.forget-password-container {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;

  .title {
    display: block;
    font-size: 48rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }

  .subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

.form-container {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;

  .form-group {
    margin-bottom: 40rpx;

    .label {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 15rpx;
    }

    .input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      height: 88rpx;
      border: 1rpx solid #e0e0e0;
      border-radius: 12rpx;
      background: #fafafa;

      &:focus-within {
        border-color: #007aff;
        background: #ffffff;
      }

      .input {
        flex: 1;
        height: 100%;
        padding: 0 20rpx;
        font-size: 30rpx;
        color: #333;
        background: transparent;
        border: none;
      }

      .eye-btn {
        padding: 0 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &.code-wrapper {
        .code-input {
          flex: 1;
        }

        .code-btn {
          height: 60rpx;
          padding: 0 20rpx;
          margin-right: 15rpx;
          background: #007aff;
          border: none;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          &:disabled {
            background: #cccccc;
          }

          .code-btn-text {
            font-size: 24rpx;
            color: #ffffff;
          }
        }
      }
    }
  }

  .submit-btn {
    width: 100%;
    height: 88rpx;
    background: #007aff;
    border: none;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;

    &:disabled {
      background: #cccccc;
    }

    .submit-text {
      font-size: 32rpx;
      color: #ffffff;
      font-weight: 500;
    }
  }

  .tips {
    .tip-text {
      display: block;
      font-size: 24rpx;
      color: #999;
      line-height: 1.6;
      margin-bottom: 8rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
