<template>
  <view class="auth-container">
    <view class="auth-header">
      <image class="auth-icon" src="/static/icons/auth.png" mode="aspectFit"></image>
      <text class="auth-title">实名认证</text>
      <text class="auth-desc">为了保障您的账户安全，请完成实名认证</text>
    </view>

    <!-- 已认证状态 -->
    <view class="auth-success" v-if="userInfo.authStatus === '1'">
      <u-icon name="checkmark-circle" size="120" color="#4caf50"></u-icon>
      <text class="success-title">实名认证成功</text>
      <view class="auth-info">
        <view class="info-item">
          <text class="info-label">真实姓名：</text>
          <text class="info-value">{{ hideName(userInfo.realName) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">身份证号：</text>
          <text class="info-value">{{ hideIdCard(userInfo.idCard) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">认证时间：</text>
          <text class="info-value">{{ formatTime(userInfo.updateTime) }}</text>
        </view>
      </view>
    </view>

    <!-- 未认证状态 -->
    <view class="auth-form" v-else>
      <view class="form-container">
        <view class="form-item">
          <text class="label">真实姓名 *</text>
          <u-input v-model="form.realName" placeholder="请输入真实姓名" :clearable="true" maxlength="20" />
        </view>

        <view class="form-item">
          <text class="label">身份证号 *</text>
          <u-input v-model="form.idCard" placeholder="请输入身份证号" :clearable="true" maxlength="18" />
        </view>

        <!-- 身份证照片上传 -->
        <view class="upload-section">
          <text class="upload-title">身份证照片</text>
          <view class="upload-grid">
            <view class="upload-item">
              <view class="upload-box" @click="uploadIdCardFront">
                <image v-if="idCardFront" :src="getImageUrl(idCardFront)" mode="aspectFill" class="upload-image">
                </image>
                <view v-else class="upload-placeholder">
                  <u-icon name="camera" size="60" color="#ccc"></u-icon>
                  <text class="upload-text">身份证正面</text>
                </view>
              </view>
            </view>

            <view class="upload-item">
              <view class="upload-box" @click="uploadIdCardBack">
                <image v-if="idCardBack" :src="getImageUrl(idCardBack)" mode="aspectFill" class="upload-image"></image>
                <view v-else class="upload-placeholder">
                  <u-icon name="camera" size="60" color="#ccc"></u-icon>
                  <text class="upload-text">身份证反面</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 协议同意 -->
        <view class="agreement-row">
          <u-checkbox v-model="agreed" :size="28">
            我已阅读并同意
          </u-checkbox>
          <text class="link" @click="showAgreement">《实名认证协议》</text>
        </view>

        <!-- 提交按钮 -->
        <u-button type="primary" size="large" :loading="submitting" :disabled="!canSubmit" @click="submitAuth">
          提交认证
        </u-button>

        <!-- 温馨提示 -->
        <view class="tips-card">
          <view class="tips-title">温馨提示</view>
          <view class="tips-content">
            <text class="tip-item">• 请确保身份信息真实有效</text>
            <text class="tip-item">• 身份证照片需清晰完整</text>
            <text class="tip-item">• 认证信息一经提交不可修改</text>
            <text class="tip-item">• 认证通过后可享受更多服务</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getUserInfo, realNameAuth } from '@/api/auth'
import { validateIdCard, chooseImage, formatTime } from '@/utils/common'
import { getImageUrl } from '@/utils/request'

export default {
  data() {
    return {
      userInfo: {},
      form: {
        realName: '',
        idCard: ''
      },
      idCardFront: '',
      idCardBack: '',
      agreed: false,
      submitting: false
    }
  },

  computed: {
    canSubmit() {
      return this.form.realName &&
        this.form.idCard &&
        this.idCardFront &&
        this.idCardBack &&
        this.agreed &&
        !this.submitting
    }
  },

  onLoad() {
    this.loadUserInfo()
  },

  methods: {
    // 获取图片完整URL
    getImageUrl,

    async loadUserInfo() {
      try {
        const res = await getUserInfo()
        this.userInfo = res.data || {}
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    async uploadIdCardFront() {
      try {
        const images = await chooseImage(1)
        if (images.length > 0) {
          this.idCardFront = images[0]
        }
      } catch (error) {
        console.error('选择图片失败:', error)
      }
    },

    async uploadIdCardBack() {
      try {
        const images = await chooseImage(1)
        if (images.length > 0) {
          this.idCardBack = images[0]
        }
      } catch (error) {
        console.error('选择图片失败:', error)
      }
    },

    async submitAuth() {
      if (!this.canSubmit) return

      // 验证身份证号格式
      if (!validateIdCard(this.form.idCard)) {
        uni.showToast({
          title: '身份证号格式不正确',
          icon: 'none'
        })
        return
      }

      this.submitting = true
      try {
        await realNameAuth(this.form)

        uni.showToast({
          title: '提交成功，等待审核',
          icon: 'success'
        })

        // 刷新用户信息
        setTimeout(() => {
          this.loadUserInfo()
        }, 1500)

      } catch (error) {
        console.error('实名认证失败:', error)
      } finally {
        this.submitting = false
      }
    },

    showAgreement() {
      uni.showModal({
        title: '实名认证协议',
        content: '为了保障您的账户安全和合规要求，我们需要验证您的身份信息。您提供的信息将严格保密，仅用于身份验证。',
        showCancel: false
      })
    },

    hideName(name) {
      if (!name) return ''
      if (name.length <= 2) return name
      return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1)
    },

    hideIdCard(idCard) {
      if (!idCard) return ''
      if (idCard.length <= 8) return idCard
      return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4)
    },

    formatTime
  }
}
</script>

<style lang="scss" scoped>
.auth-container {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 40rpx;
}

.auth-header {
  text-align: center;
  margin-bottom: 60rpx;

  .auth-icon {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 30rpx;
  }

  .auth-title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .auth-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
}

.auth-success {
  text-align: center;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 80rpx 40rpx;

  .success-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #4caf50;
    margin: 30rpx 0 50rpx;
  }

  .auth-info {
    text-align: left;

    .info-item {
      display: flex;
      margin-bottom: 20rpx;

      .info-label {
        font-size: 28rpx;
        color: #666;
        width: 160rpx;
      }

      .info-value {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }
    }
  }
}

.auth-form {
  .form-container {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 40rpx;
  }

  .form-item {
    margin-bottom: 40rpx;

    .label {
      display: block;
      font-size: 30rpx;
      color: #333;
      margin-bottom: 20rpx;
    }
  }

  .upload-section {
    margin-bottom: 40rpx;

    .upload-title {
      display: block;
      font-size: 30rpx;
      color: #333;
      margin-bottom: 30rpx;
    }

    .upload-grid {
      display: flex;
      gap: 30rpx;

      .upload-item {
        flex: 1;

        .upload-box {
          width: 100%;
          height: 200rpx;
          border: 2rpx dashed #ddd;
          border-radius: 20rpx;
          position: relative;
          overflow: hidden;

          .upload-image {
            width: 100%;
            height: 100%;
          }

          .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;

            .upload-text {
              font-size: 24rpx;
              color: #999;
              margin-top: 10rpx;
            }
          }
        }
      }
    }
  }

  .agreement-row {
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;
    font-size: 26rpx;
    color: #666;

    .link {
      color: #3cc51f;
      margin-left: 10rpx;
    }
  }

  .tips-card {
    background: #fff7f0;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-top: 40rpx;
    border-left: 8rpx solid #ff6b35;

    .tips-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #ff6b35;
      margin-bottom: 20rpx;
    }

    .tips-content {
      .tip-item {
        display: block;
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
        margin-bottom: 10rpx;
      }
    }
  }
}
</style>
