<template>
  <view class="task-list-container">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" :class="{ active: currentFilter === 'all' }" @click="setFilter('all')">
        全部
      </view>
      <view class="filter-item" :class="{ active: currentFilter === 'nearby' }" @click="setFilter('nearby')">
        附近
      </view>
      <view class="filter-item" :class="{ active: currentFilter === 'urgent' }" @click="setFilter('urgent')">
        紧急
      </view>
      <view class="filter-item" :class="{ active: currentFilter === 'processing' }" @click="setFilter('processing')">
        进行中
      </view>
      <view class="filter-item" :class="{ active: currentFilter === 'my' }" @click="setFilter('my')">
        我的
      </view>
    </view>

    <!-- 任务列表 -->
    <scroll-view class="task-scroll" scroll-y @scrolltolower="loadMore" refresher-enabled @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing">
      <view class="task-list">
        <view class="task-item" v-for="task in taskList" :key="task.taskId" @click="goTaskDetail(task.taskId)">
          <view class="task-header">
            <view class="user-info">
              <image class="avatar" :src="getImageUrl(task.publisherAvatar) || '/static/default-avatar.png'"
                mode="aspectFill" ></image>
              <view class="user-detail">
                <text class="username">{{ task.publisherName || '匿名用户' }}</text>
                <text class="publish-info">{{ formatTime(task.createTime) }} · {{ task.taskAddress }}</text>
                <!-- 调试信息 -->
                <text class="debug-info" v-if="showDebug">头像: {{ task.publisherAvatar || '无' }}</text>
              </view>
            </view>
            <view class="task-status" :class="getStatusClass(task.taskStatus)">
              {{ getStatusText(task.taskStatus) }}
            </view>
          </view>

          <view class="task-amount">¥{{ formatMoney(task.taskAmount) }}</view>
          <view class="task-title">{{ task.taskTitle }}</view>
          <view class="task-desc">{{ task.taskDesc }}</view>

          <view class="task-footer">
            <view class="task-meta">
              <text class="view-count">{{ task.viewCount || 0 }}次浏览</text>
              <view class="hot-score" v-if="task.hotScore > 0">
                <text class="hot-icon">🔥</text>
                <text class="hot-text">{{ task.hotScore }}</text>
              </view>
            </view>
            <view class="task-tags">
              <view class="task-type" v-if="task.taskType === '1'">
                <text class="urgent-tag">紧急</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <u-loading-icon v-if="loading"></u-loading-icon>
        <text class="load-text">{{ loading ? '加载中...' : '上拉加载更多' }}</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" v-if="!hasMore && taskList.length > 0">
        <text>没有更多任务了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && taskList.length === 0">
        <image class="empty-image" src="/static/empty-task.png" mode="aspectFit"></image>
        <text class="empty-text">暂无任务</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getTaskList, getMyPublishedTasks, getMyReceivedTasks } from '@/api/task'
import { formatTime, formatMoney, getCurrentLocation, checkLogin } from '@/utils/common'
import { getImageUrl } from '@/utils/request'

export default {
  data() {
    return {
      currentFilter: 'all',
      taskList: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      page: 1,
      pageSize: 10,
      longitude: '',
      latitude: '',
      showDebug: false // 调试模式开关
    }
  },

  onLoad() {
    this.getLocation()
    this.loadTaskList()
  },

  onShow() {
    // 从发布页面返回时刷新列表
    if (this.taskList.length > 0) {
      this.onRefresh()
    }
  },

  methods: {
    // 获取图片完整URL
    getImageUrl,

    async getLocation() {
      try {
        const location = await getCurrentLocation()
        this.longitude = location.longitude
        this.latitude = location.latitude
      } catch (error) {
        console.error('获取位置失败:', error)
      }
    },

    setFilter(filter) {
      this.currentFilter = filter
      this.page = 1
      this.hasMore = true
      this.taskList = []
      this.loadTaskList()
    },

    async loadTaskList() {
      if (this.loading || !this.hasMore) return

      this.loading = true
      try {
        const params = {
          pageNum: this.page,
          pageSize: this.pageSize,
          longitude: this.longitude,
          latitude: this.latitude
        }

        let res

        // 根据筛选条件调用不同的API
        switch (this.currentFilter) {
          case 'my':
            // 我的任务：需要登录验证
            if (!checkLogin()) return
            // 获取我发布的和我接取的任务
            const [publishedRes, receivedRes] = await Promise.all([
              getMyPublishedTasks(),
              getMyReceivedTasks()
            ])
            const publishedTasks = publishedRes.data || []
            const receivedTasks = receivedRes.data || []
            // 合并并去重
            const allMyTasks = [...publishedTasks, ...receivedTasks]
            const uniqueTasks = allMyTasks.filter((task, index, self) =>
              index === self.findIndex(t => t.taskId === task.taskId)
            )
            // 对于"我的"任务，禁用分页加载更多功能
            this.hasMore = false
            res = { rows: uniqueTasks }
            break
          case 'processing':
            // 进行中的任务
            params.taskStatus = '1'
            res = await getTaskList(params)
            break
          case 'nearby':
            // 附近任务需要位置信息
            res = await getTaskList(params)
            break
          case 'urgent':
            params.taskType = '1'
            res = await getTaskList(params)
            break
          default:
            // 全部任务
            res = await getTaskList(params)
            break
        }
        const newTasks = res.rows || []

        if (this.page === 1) {
          this.taskList = newTasks
        } else {
          this.taskList.push(...newTasks)
        }

        this.hasMore = newTasks.length === this.pageSize
        this.page++

      } catch (error) {
        console.error('加载任务列表失败:', error)
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    loadMore() {
      this.loadTaskList()
    },

    onRefresh() {
      this.refreshing = true
      this.page = 1
      this.hasMore = true
      this.taskList = []
      this.loadTaskList()
    },

    goTaskDetail(taskId) {
      uni.navigateTo({
        url: `/pages/task/detail?id=${taskId}`
      })
    },


    getStatusText(status) {
      const statusMap = {
        '0': '待接取',
        '1': '进行中',
        '2': '已完成',
        '3': '已取消'
      }
      return statusMap[status] || '未知'
    },

    getStatusClass(status) {
      const classMap = {
        '0': 'waiting',
        '1': 'processing',
        '2': 'completed',
        '3': 'cancelled'
      }
      return classMap[status] || ''
    },

    formatTime,
    formatMoney
  }
}
</script>

<style lang="scss" scoped>
.task-list-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f8f8;
}

.filter-bar {
  display: flex;
  background: #ffffff;
  padding: 20rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .filter-item {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    font-size: 28rpx;
    color: #666;
    border-radius: 40rpx;
    margin: 0 10rpx;

    &.active {
      background: #3cc51f;
      color: #ffffff;
    }
  }
}

.task-scroll {
  flex: 1;
}

.task-list {
  padding: 20rpx 40rpx;

  .task-item {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .user-info {
        display: flex;
        align-items: center;

        .avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 40rpx;
          margin-right: 20rpx;
        }

        .user-detail {
          .username {
            display: block;
            font-size: 28rpx;
            color: #333;
            margin-bottom: 5rpx;
          }

          .publish-info {
            font-size: 24rpx;
            color: #999;
          }
        }
      }

      .task-status {
        padding: 10rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;

        &.waiting {
          background: #e8f5e8;
          color: #3cc51f;
        }

        &.processing {
          background: #fff3e0;
          color: #ff9800;
        }

        &.completed {
          background: #e3f2fd;
          color: #2196f3;
        }

        &.cancelled {
          background: #ffebee;
          color: #f44336;
        }
      }
    }

    .task-amount {
      font-size: 36rpx;
      font-weight: bold;
      color: #ff6b35;
      margin-bottom: 10rpx;
    }

    .task-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }

    .task-desc {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
      margin-bottom: 20rpx;
    }

    .task-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .task-meta {
        display: flex;
        gap: 20rpx;

        .view-count {
          font-size: 24rpx;
          color: #999;
        }

        .hot-score {
          display: flex;
          align-items: center;
          background: linear-gradient(135deg, #ff6b35, #ff8c42);
          color: #ffffff;
          padding: 6rpx 12rpx;
          border-radius: 20rpx;
          font-size: 22rpx;
          font-weight: bold;
          box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);

          .hot-icon {
            margin-right: 4rpx;
            font-size: 20rpx;
          }

          .hot-text {
            font-size: 22rpx;
          }
        }
      }

      .task-tags {
        display: flex;
        gap: 10rpx;

        .task-type {
          .urgent-tag {
            background: #ff4757;
            color: #ffffff;
            padding: 8rpx 16rpx;
            border-radius: 16rpx;
            font-size: 22rpx;
          }
        }
      }
    }
  }
}

.load-more,
.no-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;

  .empty-image {
    width: 300rpx;
    height: 300rpx;
    margin-bottom: 40rpx;
  }

  .empty-text {
    display: block;
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
}
</style>
