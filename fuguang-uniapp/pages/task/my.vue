<template>
  <view class="my-task-container">
    <!-- 标签切换 -->
    <view class="tab-bar">
      <view class="tab-item" :class="{ active: currentTab === 'published' }" @click="switchTab('published')">
        我发布的
      </view>
      <view class="tab-item" :class="{ active: currentTab === 'received' }" @click="switchTab('received')">
        我接取的
      </view>
    </view>
    
    <!-- 任务列表 -->
    <scroll-view 
      class="task-scroll" 
      scroll-y 
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view class="task-list">
        <view class="task-item" v-for="task in taskList" :key="task.taskId" @click="goTaskDetail(task.taskId)">
          <view class="task-header">
            <view class="task-info">
              <text class="task-title">{{ task.taskTitle }}</text>
              <text class="task-amount">¥{{ formatMoney(task.taskAmount) }}</text>
            </view>
            <view class="task-status" :class="getStatusClass(task.taskStatus)">
              {{ getStatusText(task.taskStatus) }}
            </view>
          </view>
          
          <view class="task-desc">{{ task.taskDesc }}</view>
          
          <view class="task-meta">
            <view class="meta-left">
              <text class="address">{{ task.taskAddress }}</text>
              <text class="time">{{ formatTime(task.createTime) }}</text>
            </view>
            <view class="meta-right">
              <text class="view-count">{{ task.viewCount || 0 }}次浏览</text>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="task-actions" v-if="showActions(task)">
            <!-- 我发布的任务操作 -->
            <template v-if="currentTab === 'published'">
              <u-button 
                v-if="task.taskStatus === '0'" 
                size="small" 
                type="warning" 
                @click.stop="editTask(task.taskId)"
              >
                编辑
              </u-button>
              <u-button 
                v-if="task.taskStatus === '0' || task.taskStatus === '1'" 
                size="small" 
                type="error" 
                @click.stop="cancelTask(task.taskId)"
              >
                取消
              </u-button>
            </template>
            
            <!-- 我接取的任务操作 -->
            <template v-if="currentTab === 'received'">
              <u-button 
                v-if="task.taskStatus === '1'" 
                size="small" 
                type="success" 
                @click.stop="completeTask(task.taskId)"
              >
                完成
              </u-button>
            </template>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && taskList.length === 0">
        <image class="empty-image" src="/static/empty-task.png" mode="aspectFit"></image>
        <text class="empty-text">{{ emptyText }}</text>
        <u-button 
          v-if="currentTab === 'published'" 
          type="primary" 
          size="small" 
          @click="goPublish"
        >
          发布任务
        </u-button>
        <u-button 
          v-else 
          type="primary" 
          size="small" 
          @click="goTaskList"
        >
          去接取任务
        </u-button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getMyPublishedTasks, getMyReceivedTasks, cancelTask, completeTask } from '@/api/task'
import { formatTime, formatMoney, checkLogin } from '@/utils/common'

export default {
  data() {
    return {
      currentTab: 'published',
      taskList: [],
      loading: false,
      refreshing: false
    }
  },
  
  computed: {
    emptyText() {
      return this.currentTab === 'published' ? '您还没有发布任何任务' : '您还没有接取任何任务'
    }
  },
  
  onLoad() {
    if (!checkLogin()) {
      uni.switchTab({
        url: '/pages/user/center'
      })
      return
    }
    
    this.loadTaskList()
  },
  
  onShow() {
    // 从其他页面返回时刷新列表
    if (this.taskList.length > 0) {
      this.loadTaskList()
    }
  },
  
  methods: {
    switchTab(tab) {
      this.currentTab = tab
      this.taskList = []
      this.loadTaskList()
    },
    
    async loadTaskList() {
      this.loading = true
      try {
        let res
        if (this.currentTab === 'published') {
          res = await getMyPublishedTasks()
        } else {
          res = await getMyReceivedTasks()
        }
        
        this.taskList = res.data || []
      } catch (error) {
        console.error('加载任务列表失败:', error)
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    onRefresh() {
      this.refreshing = true
      this.loadTaskList()
    },
    
    showActions(task) {
      if (this.currentTab === 'published') {
        return task.taskStatus === '0' || task.taskStatus === '1'
      } else {
        return task.taskStatus === '1'
      }
    },
    
    goTaskDetail(taskId) {
      uni.navigateTo({
        url: `/pages/task/detail?id=${taskId}`
      })
    },
    
    editTask(taskId) {
      uni.navigateTo({
        url: `/pages/task/publish?id=${taskId}`
      })
    },
    
    async cancelTask(taskId) {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消这个任务吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await cancelTask(taskId)
              uni.showToast({
                title: '任务已取消',
                icon: 'success'
              })
              this.loadTaskList()
            } catch (error) {
              console.error('取消任务失败:', error)
            }
          }
        }
      })
    },
    
    async completeTask(taskId) {
      uni.showModal({
        title: '确认完成',
        content: '确定已完成这个任务吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await completeTask(taskId)
              uni.showToast({
                title: '任务已完成',
                icon: 'success'
              })
              this.loadTaskList()
            } catch (error) {
              console.error('完成任务失败:', error)
            }
          }
        }
      })
    },
    
    goPublish() {
      uni.switchTab({
        url: '/pages/task/publish'
      })
    },
    
    goTaskList() {
      uni.switchTab({
        url: '/pages/task/list'
      })
    },
    
    getStatusText(status) {
      const statusMap = {
        '0': '待接取',
        '1': '进行中',
        '2': '已完成',
        '3': '已取消'
      }
      return statusMap[status] || '未知'
    },
    
    getStatusClass(status) {
      const classMap = {
        '0': 'waiting',
        '1': 'processing',
        '2': 'completed',
        '3': 'cancelled'
      }
      return classMap[status] || ''
    },
    
    formatTime,
    formatMoney
  }
}
</script>

<style lang="scss" scoped>
.my-task-container {
  display: flex;
  flex-direction: column;
  background: #f8f8f8;
}

.tab-bar {
  display: flex;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  
  .tab-item {
    flex: 1;
    text-align: center;
    padding: 30rpx 0;
    font-size: 30rpx;
    color: #666;
    position: relative;
    
    &.active {
      color: #3cc51f;
      font-weight: bold;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 6rpx;
        background: #3cc51f;
        border-radius: 3rpx;
      }
    }
  }
}

.task-scroll {
  flex: 1;
}

.task-list {
  padding: 20rpx 40rpx;
  
  .task-item {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    
    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20rpx;
      
      .task-info {
        flex: 1;
        
        .task-title {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 10rpx;
          line-height: 1.4;
        }
        
        .task-amount {
          font-size: 36rpx;
          font-weight: bold;
          color: #ff6b35;
        }
      }
      
      .task-status {
        padding: 10rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        white-space: nowrap;
        
        &.waiting {
          background: #e8f5e8;
          color: #3cc51f;
        }
        
        &.processing {
          background: #fff3e0;
          color: #ff9800;
        }
        
        &.completed {
          background: #e3f2fd;
          color: #2196f3;
        }
        
        &.cancelled {
          background: #ffebee;
          color: #f44336;
        }
      }
    }
    
    .task-desc {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
      margin-bottom: 20rpx;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
    
    .task-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .meta-left {
        .address {
          display: block;
          font-size: 26rpx;
          color: #999;
          margin-bottom: 5rpx;
        }
        
        .time {
          font-size: 24rpx;
          color: #ccc;
        }
      }
      
      .meta-right {
        .view-count {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
    
    .task-actions {
      display: flex;
      gap: 20rpx;
      justify-content: flex-end;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  
  .empty-image {
    width: 300rpx;
    height: 300rpx;
    margin-bottom: 40rpx;
  }
  
  .empty-text {
    display: block;
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
}
</style>
