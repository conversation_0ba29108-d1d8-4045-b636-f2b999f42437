<template>
  <view class="settings-container">
    <!-- 用户信息区域 -->
    <view class="user-info-section" v-if="isLoggedIn" @click="goProfile">
      <view class="user-avatar">
        <image class="avatar" :src="getImageUrl(userInfo.avatar) || '/static/default-avatar.png'" mode="aspectFill">
        </image>
      </view>
      <view class="user-details">
        <text class="nickname">{{ userInfo.nickName || '未设置昵称' }}</text>
        <text class="phone">{{ formatPhone(userInfo.phonenumber) || '未绑定手机' }}</text>
      </view>
      <u-icon name="arrow-right" size="16" color="#999"></u-icon>
    </view>

    <!-- 未登录状态 -->
    <view class="login-prompt" v-else @click="goLogin">
      <view class="login-avatar">
        <image class="avatar" src="/static/default-avatar.png" mode="aspectFill"></image>
      </view>
      <view class="login-text">
        <text class="login-title">点击登录</text>
        <text class="login-desc">登录后享受更多服务</text>
      </view>
      <u-icon name="arrow-right" size="16" color="#999"></u-icon>
    </view>

    <view class="settings-list">
      <!-- 账号管理区域 -->
      <view class="settings-group" v-if="isLoggedIn">
        <view class="group-title">账号管理</view>

        <view class="settings-item" @click="goAuth">
          <view class="item-left">
            <u-icon name="checkmark-circle" size="20" color="#4caf50" v-if="userInfo.authStatus === '1'"></u-icon>
            <u-icon name="info-circle" size="20" color="#ff9800" v-else></u-icon>
            <text class="item-text">实名认证</text>
            <text class="status-text" v-if="userInfo.authStatus === '1'">已认证</text>
            <text class="status-text unauth" v-else>未认证</text>
          </view>
          <u-icon name="arrow-right" size="16" color="#999"></u-icon>
        </view>

        <view class="settings-item" @click="goForgetPassword">
          <view class="item-left">
            <u-icon name="lock" size="20" color="#666"></u-icon>
            <text class="item-text">修改密码</text>
          </view>
          <u-icon name="arrow-right" size="16" color="#999"></u-icon>
        </view>

        <view class="settings-item" @click="deleteAccount">
          <view class="item-left">
            <u-icon name="trash" size="20" color="#ff4757"></u-icon>
            <text class="item-text danger-text">注销账号</text>
          </view>
          <u-icon name="arrow-right" size="16" color="#999"></u-icon>
        </view>
      </view>

      <!-- 应用设置区域 -->
      <view class="settings-group">
        <view class="group-title">应用设置</view>

        <view class="settings-item" @click="checkUpdate">
          <view class="item-left">
            <u-icon name="reload" size="20" color="#666"></u-icon>
            <text class="item-text">检查更新</text>
            <text class="version-text">v1.0.0</text>
          </view>
          <u-icon name="arrow-right" size="16" color="#999"></u-icon>
        </view>

        <view class="settings-item" @click="goAbout">
          <view class="item-left">
            <u-icon name="info-circle" size="20" color="#666"></u-icon>
            <text class="item-text">关于浮光</text>
          </view>
          <u-icon name="arrow-right" size="16" color="#999"></u-icon>
        </view>

        <view class="settings-item" @click="goFeedback">
          <view class="item-left">
            <u-icon name="question-circle" size="20" color="#666"></u-icon>
            <text class="item-text">帮助反馈</text>
          </view>
          <u-icon name="arrow-right" size="16" color="#999"></u-icon>
        </view>
      </view>

      <!-- 其他设置区域 -->
      <view class="settings-group">
        <view class="group-title">其他</view>

        <view class="settings-item" @click="goPrivacy">
          <view class="item-left">
            <u-icon name="chat" size="20" color="#666"></u-icon>
            <text class="item-text">隐私政策</text>
          </view>
          <u-icon name="arrow-right" size="16" color="#999"></u-icon>
        </view>

        <view class="settings-item" @click="goUserAgreement">
          <view class="item-left">
            <u-icon name="file-text" size="20" color="#666"></u-icon>
            <text class="item-text">用户协议</text>
          </view>
          <u-icon name="arrow-right" size="16" color="#999"></u-icon>
        </view>
      </view>

      <!-- 退出登录 -->
      <view class="settings-group" v-if="isLoggedIn">
        <view class="settings-item logout-item" @click="logout">
          <u-icon name="close-circle" size="20" color="#ff4757"></u-icon>
          <text class="logout-text">退出登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getUserInfo, deleteAccount } from '@/api/auth'
import { getImageUrl } from '@/utils/request'

export default {
  data() {
    return {
      userInfo: {}
    }
  },

  computed: {
    isLoggedIn() {
      return !!uni.getStorageSync('token')
    }
  },

  onShow() {
    if (this.isLoggedIn) {
      this.loadUserInfo()
    }
  },

  methods: {
    // 获取图片完整URL
    getImageUrl,

    async loadUserInfo() {
      try {
        const res = await getUserInfo()
        this.userInfo = res.data || {}
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    formatPhone(phone) {
      if (!phone) return ''
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    },

    goLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    },

    goProfile() {
      uni.navigateTo({
        url: '/pages/user/profile'
      })
    },

    goAuth() {
      uni.navigateTo({
        url: '/pages/user/auth'
      })
    },

    goForgetPassword() {
      uni.navigateTo({
        url: '/pages/user/forget-password'
      })
    },

    deleteAccount() {
      uni.showModal({
        title: '注销账号',
        content: '注销后将无法恢复账号数据，确定要注销吗？',
        confirmText: '确定注销',
        confirmColor: '#ff4757',
        success: (res) => {
          if (res.confirm) {
            this.confirmDeleteAccount()
          }
        }
      })
    },

    async confirmDeleteAccount() {
      uni.showModal({
        title: '最后确认',
        content: '请输入"确认注销"来确认删除账号',
        editable: true,
        placeholderText: '请输入：确认注销',
        success: async (res) => {
          if (res.confirm && res.content === '确认注销') {
            uni.showLoading({
              title: '注销中...'
            })

            try {
              await deleteAccount({
                reason: '用户主动注销'
              })

              uni.hideLoading()
              uni.showModal({
                title: '注销成功',
                content: '账号已成功注销，感谢您的使用',
                showCancel: false,
                success: () => {
                  // 清除本地数据
                  uni.removeStorageSync('token')
                  uni.removeStorageSync('userInfo')

                  // 跳转到登录页
                  uni.reLaunch({
                    url: '/pages/login/login'
                  })
                }
              })

            } catch (error) {
              uni.hideLoading()
              console.error('注销账号失败:', error)
              uni.showToast({
                title: error.message || '注销失败',
                icon: 'none'
              })
            }
          } else if (res.confirm) {
            uni.showToast({
              title: '输入错误，请重新输入',
              icon: 'none'
            })
          }
        }
      })
    },

    checkUpdate() {
      uni.showLoading({
        title: '检查中...'
      })

      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '已是最新版本',
          icon: 'success'
        })
      }, 1500)
    },

    goAbout() {
      uni.navigateTo({
        url: '/pages/about/index'
      })
    },

    goFeedback() {
      uni.navigateTo({
        url: '/pages/feedback/index'
      })
    },

    goPrivacy() {
      uni.navigateTo({
        url: '/pages/agreement/privacy'
      })
    },

    goUserAgreement() {
      uni.navigateTo({
        url: '/pages/agreement/user'
      })
    },

    logout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            uni.removeStorageSync('token')
            uni.removeStorageSync('userInfo')

            uni.showToast({
              title: '已退出登录',
              icon: 'success'
            })

            // 返回上一页并刷新
            setTimeout(() => {
              uni.navigateBack()
            }, 1500)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-container {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 20rpx;
}

// 用户信息区域
.user-info-section {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;

  &:active {
    background: #f8f9fa;
  }

  .user-avatar {
    margin-right: 20rpx;

    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      background: #f0f0f0;
    }
  }

  .user-details {
    flex: 1;

    .nickname {
      display: block;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
    }

    .phone {
      display: block;
      font-size: 26rpx;
      color: #666;
    }
  }
}

// 未登录状态
.login-prompt {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;

  &:active {
    background: #f8f9fa;
  }

  .login-avatar {
    margin-right: 20rpx;

    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      background: #f0f0f0;
    }
  }

  .login-text {
    flex: 1;

    .login-title {
      display: block;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
    }

    .login-desc {
      display: block;
      font-size: 26rpx;
      color: #666;
    }
  }
}

.settings-list {
  .settings-group {
    background: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    overflow: hidden;

    .group-title {
      padding: 20rpx 30rpx 10rpx;
      font-size: 26rpx;
      color: #999;
      background: #fafafa;
      border-bottom: 1rpx solid #f0f0f0;
    }
  }

  .settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background: #f8f9fa;
    }

    .item-left {
      display: flex;
      align-items: center;
      flex: 1;

      .item-text {
        margin-left: 20rpx;
        font-size: 30rpx;
        color: #333;
        flex: 1;
      }

      .status-text {
        font-size: 24rpx;
        color: #4caf50;
        margin-left: 10rpx;

        &.unauth {
          color: #ff9800;
        }
      }

      .version-text {
        font-size: 24rpx;
        color: #999;
        margin-left: 10rpx;
      }

      .danger-text {
        color: #ff4757;
      }
    }

    &.logout-item {
      justify-content: center;
      align-items: center;
      gap: 15rpx;

      .logout-text {
        color: #ff4757;
        font-size: 30rpx;
        font-weight: 500;
      }
    }
  }
}
</style>
