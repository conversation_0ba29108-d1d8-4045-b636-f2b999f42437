// 通用工具函数

// 格式化时间
export const formatTime = (date) => {
  if (!date) return "";

  const now = new Date();
  const target = new Date(date);
  const diff = now.getTime() - target.getTime();

  const minute = 1000 * 60;
  const hour = minute * 60;
  const day = hour * 24;
  const month = day * 30;

  // 如果是未来时间（diff为负数），显示具体时间
  if (diff < 0) {
    const absDiff = Math.abs(diff);
    if (absDiff < minute) {
      return "即将开始";
    } else if (absDiff < hour) {
      return Math.floor(absDiff / minute) + "分钟后";
    } else if (absDiff < day) {
      return Math.floor(absDiff / hour) + "小时后";
    } else if (absDiff < month) {
      return Math.floor(absDiff / day) + "天后";
    } else {
      return target.toLocaleDateString();
    }
  }

  // 过去时间的处理
  if (diff < minute) {
    return "刚刚";
  } else if (diff < hour) {
    return Math.floor(diff / minute) + "分钟前";
  } else if (diff < day) {
    return Math.floor(diff / hour) + "小时前";
  } else if (diff < month) {
    return Math.floor(diff / day) + "天前";
  } else {
    return target.toLocaleDateString();
  }
};

// 格式化金额
export const formatMoney = (amount) => {
  if (!amount) return "0.00";
  return parseFloat(amount).toFixed(2);
};

// 获取当前位置
export const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type: "gcj02",
      success: (res) => {
        resolve({
          longitude: res.longitude,
          latitude: res.latitude,
          address: res.address || "",
        });
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};

// 检查登录状态
export const checkLogin = () => {
  const token = uni.getStorageSync("token");
  if (!token) {
    uni.showModal({
      title: "提示",
      content: "请先登录",
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: "/pages/login/login",
          });
        }
      },
    });
    return false;
  }
  return true;
};

// 防抖函数
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 节流函数
export const throttle = (func, limit) => {
  let inThrottle;
  return function () {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// 验证手机号
export const validatePhone = (phone) => {
  const reg = /^1[3-9]\d{9}$/;
  return reg.test(phone);
};

// 验证邮箱
export const validateEmail = (email) => {
  const reg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return reg.test(email);
};

// 验证身份证号
export const validateIdCard = (idCard) => {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return reg.test(idCard);
};

// 复制到剪贴板
export const copyToClipboard = (text) => {
  uni.setClipboardData({
    data: text,
    success: () => {
      uni.showToast({
        title: "复制成功",
        icon: "success",
      });
    },
  });
};

// 拨打电话
export const makePhoneCall = (phoneNumber) => {
  uni.makePhoneCall({
    phoneNumber,
  });
};

// 预览图片
export const previewImage = (urls, current = 0) => {
  uni.previewImage({
    urls,
    current,
  });
};

// 选择图片
export const chooseImage = (count = 1) => {
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count,
      sizeType: ["compressed"],
      sourceType: ["album", "camera"],
      success: (res) => {
        resolve(res.tempFilePaths);
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};

// 扫码
export const scanCode = () => {
  return new Promise((resolve, reject) => {
    uni.scanCode({
      success: (res) => {
        resolve(res.result);
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};
