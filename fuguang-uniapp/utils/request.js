// API请求封装
import config from "@/config";
const BASE_URL = config.baseURL;

// 请求拦截器
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 获取token
    const token = uni.getStorageSync("token");

    // 设置请求头
    const header = {
      "Content-Type": "application/json",
      ...options.header,
    };

    // 如果有token，添加到请求头
    if (token) {
      header.Authorization = `Bearer ${token}`;
    }

    uni.request({
      url: BASE_URL + options.url,
      method: options.method || "GET",
      data: options.data || {},
      header,
      success: (res) => {
        // 统一处理响应
        if (res.statusCode === 200) {
          const data = res.data;
          if (data.code === 200) {
            resolve(data);
          }else if(data.code===401){
			  // token过期，跳转登录
			  uni.removeStorageSync("token");
			  uni.removeStorageSync("userInfo");
			  uni.reLaunch({
			    url: "/pages/login/login",
			  });
			  reject(res);
		  } else {
            // 业务错误
            uni.showToast({
              title: data.msg || "请求失败",
              icon: "none",
            });
            reject(data);
          }
        } else if (res.statusCode === 401) {
          // token过期，跳转登录
          uni.removeStorageSync("token");
          uni.removeStorageSync("userInfo");
          uni.reLaunch({
            url: "/pages/login/login",
          });
          reject(res);
        } else {
          // 其他错误
          uni.showToast({
            title: "网络错误",
            icon: "none",
          });
          reject(res);
        }
      },
      fail: (err) => {
        uni.showToast({
          title: "网络连接失败",
          icon: "none",
        });
        reject(err);
      },
    });
  });
};

// 公共请求（不需要token）
const publicRequest = (options) => {
  return new Promise((resolve, reject) => {
    // 设置请求头
    const header = {
      "Content-Type": "application/json",
      ...options.header,
    };

    uni.request({
      url: BASE_URL + options.url,
      method: options.method || "GET",
      data: options.data || {},
      header,
      success: (res) => {
        // 统一处理响应
        if (res.statusCode === 200) {
          const data = res.data;
          if (data.code === 200) {
            resolve(data);
          } else {
            // 业务错误
            uni.showToast({
              title: data.msg || "请求失败",
              icon: "none",
            });
            reject(data);
          }
        } else if (res.statusCode === 401) {
          // token过期，跳转登录
          uni.removeStorageSync("token");
          uni.removeStorageSync("userInfo");
          uni.reLaunch({
            url: "/pages/login/login",
          });
          reject(res);
        } else {
          // 其他错误
          uni.showToast({
            title: "网络错误",
            icon: "none",
          });
          reject(res);
        }
      },
      fail: (err) => {
        uni.showToast({
          title: "网络连接失败",
          icon: "none",
        });
        reject(err);
      },
    });
  });
};

// GET请求
export const get = (url, data = {}) => {
  return request({
    url,
    method: "GET",
    data,
  });
};

// 公共GET请求（不需要token）
export const publicGet = (url, data = {}) => {
  return publicRequest({
    url,
    method: "GET",
    data,
  });
};

// POST请求
export const post = (url, data = {}) => {
  return request({
    url,
    method: "POST",
    data,
  });
};

// PUT请求
export const put = (url, data = {}) => {
  return request({
    url,
    method: "PUT",
    data,
  });
};

// DELETE请求
export const del = (url, data = {}) => {
  return request({
    url,
    method: "DELETE",
    data,
  });
};

// 文件上传
export const upload = (url, filePath, name = "file") => {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync("token");

    uni.uploadFile({
      url: BASE_URL + url,
      filePath,
      name,
      header: {
        Authorization: token ? `Bearer ${token}` : "",
      },
      success: (res) => {
        if (res.statusCode === 401) {
          // token过期，跳转登录
          uni.removeStorageSync("token");
          uni.removeStorageSync("userInfo");
          uni.reLaunch({
            url: "/pages/login/login",
          });
          reject(res);
          return;
        }

        const data = JSON.parse(res.data);
        if (data.code === 200) {
          resolve(data);
        } else {
          uni.showToast({
            title: data.msg || "上传失败",
            icon: "none",
          });
          reject(data);
        }
      },
      fail: (err) => {
        uni.showToast({
          title: "上传失败",
          icon: "none",
        });
        reject(err);
      },
    });
  });
};

// 图片URL处理函数
export const getImageUrl = (imagePath) => {
  if (!imagePath) {
    return "";
  }

  // 如果已经是完整的URL（包含http或https），直接返回
  if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
    return imagePath;
  }

  // 如果是相对路径，拼接服务器地址
  if (imagePath.startsWith("/")) {
    return BASE_URL + imagePath;
  }

  // 其他情况，也拼接服务器地址
  return BASE_URL + "/" + imagePath;
};

export default request;
