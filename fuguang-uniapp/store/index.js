import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    // 用户信息
    userInfo: {},
    // 登录状态
    isLoggedIn: false,
    // 位置信息
    location: {
      longitude: "",
      latitude: "",
      address: "",
    },
    // 未读消息数
    unreadCount: 0,
    // 系统配置
    appConfig: {},
    // 网络状态
    networkStatus: true,
  },

  mutations: {
    // 设置用户信息
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo;
      state.isLoggedIn = !!userInfo.userId;
    },

    // 清除用户信息
    CLEAR_USER_INFO(state) {
      state.userInfo = {};
      state.isLoggedIn = false;
    },

    // 设置位置信息
    SET_LOCATION(state, location) {
      state.location = location;
    },

    // 设置未读消息数
    SET_UNREAD_COUNT(state, count) {
      state.unreadCount = count;
    },

    // 设置APP配置
    SET_APP_CONFIG(state, config) {
      state.appConfig = config;
    },

    // 设置网络状态
    SET_NETWORK_STATUS(state, status) {
      state.networkStatus = status;
    },
  },

  actions: {
    // 登录
    login({ commit }, userInfo) {
      commit("SET_USER_INFO", userInfo);
      uni.setStorageSync("userInfo", userInfo);
    },

    // 退出登录
    logout({ commit }) {
      commit("CLEAR_USER_INFO");
      uni.removeStorageSync("token");
      uni.removeStorageSync("userInfo");
    },

    // 更新用户信息
    updateUserInfo({ commit, state }, userInfo) {
      const newUserInfo = { ...state.userInfo, ...userInfo };
      commit("SET_USER_INFO", newUserInfo);
      uni.setStorageSync("userInfo", newUserInfo);
    },

    // 更新位置信息
    updateLocation({ commit }, location) {
      commit("SET_LOCATION", location);
    },

    // 更新未读消息数
    updateUnreadCount({ commit }, count) {
      commit("SET_UNREAD_COUNT", count);
    },

    // 初始化应用状态
    async initApp({ commit, dispatch }) {
      // 从本地存储恢复用户信息
      const userInfo = uni.getStorageSync("userInfo");
      const token = uni.getStorageSync("token");

      if (userInfo) {
        // 如果是字符串，需要解析为对象
        let user = userInfo;
        if (typeof userInfo === "string") {
          try {
            user = JSON.parse(userInfo);
          } catch (e) {
            console.error("解析用户信息失败:", e);
            return;
          }
        }
        commit("SET_USER_INFO", user);
      } else if (token) {
        // 如果有 token 但没有用户信息，尝试获取用户信息
        try {
          const { getUserInfo } = await import("@/api/auth");
          const res = await getUserInfo();
          if (res && res.data) {
            commit("SET_USER_INFO", res.data);
            uni.setStorageSync("userInfo", res.data);
          }
        } catch (error) {
          console.error("获取用户信息失败:", error);
          // 如果获取失败，可能是 token 过期，清除本地存储
          uni.removeStorageSync("token");
          uni.removeStorageSync("userInfo");
        }
      }

      // 监听网络状态
      uni.onNetworkStatusChange((res) => {
        commit("SET_NETWORK_STATUS", res.isConnected);
      });
    },
  },

  getters: {
    // 是否已登录
    isLoggedIn: (state) => state.isLoggedIn,

    // 用户信息
    userInfo: (state) => state.userInfo,

    // 用户ID
    userId: (state) => state.userInfo.userId,

    // 用户昵称
    nickName: (state) => state.userInfo.nickName,

    // 是否实名认证
    isAuthenticated: (state) => state.userInfo.authStatus === "1",

    // 当前位置
    currentLocation: (state) => state.location.address || "未知位置",

    // 是否有未读消息
    hasUnreadMessage: (state) => state.unreadCount > 0,
  },
});

export default store;
