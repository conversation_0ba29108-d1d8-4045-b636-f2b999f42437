# 浮光壁垒项目 - 任务发布系统

这是一个基于 Spring Boot + UniApp + Vue.js 的综合性应用项目，包含完整的任务发布和管理功能。

## 🚀 新增功能

### 任务发布系统

- ✅ **完整的任务发布流程**：支持任务标题、描述、金额、类型、图片、地址、紧急程度等
- ✅ **任务类型管理**：支持一级和二级任务类型的树形结构
- ✅ **智能地址选择**：集成多种地址选择方式
- ✅ **图片上传功能**：支持最多 9 张图片上传
- ✅ **管理后台**：完整的任务和任务类型管理界面

### 核心特性

1. **用户友好的发布界面**
2. **灵活的任务分类系统**
3. **多样化的地址选择方式**
4. **完善的管理后台**
5. **响应式设计**

## 📁 项目结构

```
浮光壁垒/
├── fuguang-api/          # 后端API服务 (Spring Boot)
├── fuguang-uniapp/       # 移动端应用 (UniApp)
├── fuguang-web/          # 管理后台 (Vue.js)
└── README.md            # 项目说明
```

## 🎯 任务发布功能详情

### 移动端功能 (fuguang-uniapp)

- **首页加号按钮** → 进入任务发布页面
- **任务信息填写**：
  - 任务标题和描述（带字符计数）
  - 任务金额设置
  - 任务类型选择（一级 → 二级级联）
  - 图片上传（最多 9 张）
  - 地址选择（多种方式）
  - 紧急程度设置
  - 时间设置（可选）

### 管理后台功能 (fuguang-web)

- **任务类型管理**：
  - 一级和二级任务类型的增删改查
  - 类型图标和排序管理
  - 状态控制
- **任务管理**：
  - 任务列表查看和搜索
  - 任务详情查看
  - 任务删除和管理

### 后端 API (fuguang-api)

- **任务类型 API**：提供任务类型的 CRUD 操作
- **任务管理 API**：提供任务的完整管理功能
- **文件上传 API**：支持图片上传
- **地址管理 API**：集成地址选择功能

## 🛠️ 技术栈

### 后端

- **Spring Boot 2.x**
- **MyBatis Plus**
- **MySQL 8.0**
- **Redis**
- **JWT 认证**

### 前端

- **UniApp** (移动端)
- **Vue.js 2.x** (管理后台)
- **uView UI** (移动端 UI 组件)
- **Element UI** (管理后台 UI 组件)

## 📱 使用流程

### 用户发布任务

1. 打开 APP，点击首页的"+"按钮
2. 填写任务基本信息（标题、描述、金额）
3. 选择任务类型（先选一级类型，再选二级类型）
4. 上传任务相关图片（可选）
5. 选择任务地址（当前位置/地图选点/已保存地址）
6. 设置紧急程度和时间
7. 提交发布

### 管理员管理

1. 登录管理后台
2. 在"任务类型管理"中维护任务分类
3. 在"任务管理"中查看和管理所有任务
4. 可以删除违规任务或导出数据

## 🗄️ 数据库设计

### 新增表结构

- `app_task_type`: 任务类型表（支持树形结构）
- `app_task_image`: 任务图片表
- `app_task`: 任务表（新增类型和紧急程度字段）

### 预置数据

系统预置了 5 个一级任务类型和对应的二级类型：

- 生活服务（家政清洁、维修安装、宠物服务、照看陪护）
- 跑腿代办（代买代购、文件递送、排队代办、取送物品）
- 技能服务（设计制作、教学培训、翻译服务、咨询服务）
- 搬运装卸（搬家服务、货物装卸、家具安装）
- 其他服务（临时帮工、活动协助、其他需求）

## 🚀 快速开始

### 1. 数据库初始化

```sql
-- 执行SQL脚本
source fuguang-api/sql/task_type_and_images.sql
```

### 2. 启动后端服务

```bash
cd fuguang-api
./ry.sh start
```

### 3. 启动管理后台

```bash
cd fuguang-web
npm install
npm run dev
```

### 4. 运行移动端

```bash
cd fuguang-uniapp
npm install
# 使用HBuilderX运行到对应平台
```

## 📝 更新日志

### v1.2.0 (2025-01-19)

- ✅ 新增完整的消息通知系统
- ✅ 新增通知列表页面和详情页面
- ✅ 新增通知分类筛选功能（系统、活动、任务通知）
- ✅ 新增通知已读/未读状态管理
- ✅ 新增批量标记已读和清空通知功能
- ✅ 新增通知详情页面和跳转功能
- ✅ 完善后端通知 API 接口和数据库设计

### v1.1.0 (2025-01-19)

- ✅ 新增完整的任务发布功能
- ✅ 新增任务类型管理系统
- ✅ 新增智能地址选择组件
- ✅ 新增图片上传功能
- ✅ 新增管理后台任务管理
- ✅ 优化用户体验和界面设计

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📄 许可证

本项目采用 MIT 许可证。
