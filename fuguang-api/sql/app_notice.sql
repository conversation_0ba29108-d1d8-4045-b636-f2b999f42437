-- APP通知表
DROP TABLE IF EXISTS `app_notice`;
CREATE TABLE `app_notice` (
  `notice_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `notice_title` varchar(200) NOT NULL COMMENT '通知标题',
  `notice_content` text COMMENT '通知内容',
  `notice_type` char(1) NOT NULL DEFAULT '0' COMMENT '通知类型（0系统通知 1活动通知 2任务通知）',
  `notice_status` char(1) NOT NULL DEFAULT '0' COMMENT '通知状态（0正常 1关闭）',
  `target_type` char(1) NOT NULL DEFAULT '0' COMMENT '目标类型（0全部用户 1指定用户）',
  `target_user_id` bigint(20) DEFAULT NULL COMMENT '目标用户ID',
  `is_read` char(1) NOT NULL DEFAULT '0' COMMENT '是否已读（0未读 1已读）',
  `publish_time` datetime NOT NULL COMMENT '发布时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`),
  KEY `idx_notice_type` (`notice_type`),
  KEY `idx_target_user` (`target_user_id`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP通知表';

-- 插入测试数据
INSERT INTO `app_notice` VALUES 
(1, '欢迎使用浮光壁垒APP', '欢迎您使用浮光壁垒APP！这里有丰富的任务等您来完成，您可以通过完成任务获得收益。如有任何问题，请联系客服。', '0', '0', '0', NULL, '0', NOW(), 'system', NOW(), '', NULL, '系统欢迎通知'),
(2, '系统维护通知', '系统将于今晚23:00-01:00进行维护升级，维护期间可能会影响部分功能的使用，请您合理安排时间。维护完成后系统将更加稳定，感谢您的理解与支持！', '0', '0', '0', NULL, '0', DATE_SUB(NOW(), INTERVAL 1 DAY), 'system', DATE_SUB(NOW(), INTERVAL 1 DAY), '', NULL, '系统维护通知'),
(3, '新手任务活动开启', '新手专享福利来啦！完成新手任务可获得丰厚奖励：\n1. 完成实名认证奖励10元\n2. 发布第一个任务奖励5元\n3. 完成第一个任务奖励15元\n活动时间有限，快来参与吧！', '1', '0', '0', NULL, '0', DATE_SUB(NOW(), INTERVAL 2 DAY), 'system', DATE_SUB(NOW(), INTERVAL 2 DAY), '', NULL, '新手活动通知'),
(4, '任务审核通过通知', '恭喜您！您发布的任务"帮忙搬家"已通过审核，现在可以被其他用户看到和接取了。请保持手机畅通，及时回复接取者的消息。', '2', '0', '1', 1, '0', DATE_SUB(NOW(), INTERVAL 3 HOUR), 'system', DATE_SUB(NOW(), INTERVAL 3 HOUR), '', NULL, '任务审核通知'),
(5, '任务完成确认', '您接取的任务"代买午餐"已被发布者确认完成，奖励金额20元已发放到您的账户。感谢您的优质服务！', '2', '0', '1', 1, '1', DATE_SUB(NOW(), INTERVAL 5 HOUR), 'system', DATE_SUB(NOW(), INTERVAL 5 HOUR), '', NULL, '任务完成通知'),
(6, '账户安全提醒', '为了保障您的账户安全，建议您：\n1. 定期修改登录密码\n2. 不要向他人透露验证码\n3. 发现异常及时联系客服\n您的账户安全是我们最关心的事情！', '0', '0', '0', NULL, '1', DATE_SUB(NOW(), INTERVAL 1 WEEK), 'system', DATE_SUB(NOW(), INTERVAL 1 WEEK), '', NULL, '安全提醒'),
(7, '周末特惠活动', '周末特惠活动开始啦！本周末发布任务可享受平台服务费8折优惠，接取任务完成后额外获得积分奖励。机会难得，不要错过！', '1', '0', '0', NULL, '0', DATE_SUB(NOW(), INTERVAL 12 HOUR), 'system', DATE_SUB(NOW(), INTERVAL 12 HOUR), '', NULL, '周末活动'),
(8, '实名认证成功', '恭喜您完成实名认证！现在您可以：\n1. 发布更高金额的任务\n2. 接取更多类型的任务\n3. 享受更快的提现服务\n感谢您对平台的信任！', '0', '0', '1', 1, '1', DATE_SUB(NOW(), INTERVAL 2 DAY), 'system', DATE_SUB(NOW(), INTERVAL 2 DAY), '', NULL, '认证成功通知');
