-- 创建任务类型表
CREATE TABLE `app_task_type` (
  `type_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `type_name` varchar(50) NOT NULL COMMENT '类型名称',
  `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父类型ID（0为一级类型）',
  `type_icon` varchar(255) DEFAULT NULL COMMENT '类型图标',
  `order_num` int(11) DEFAULT '0' COMMENT '排序',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `type_desc` varchar(500) DEFAULT NULL COMMENT '类型描述',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`type_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP任务类型表';

-- 创建任务图片表
CREATE TABLE `app_task_image` (
  `image_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `image_url` varchar(500) NOT NULL COMMENT '图片URL',
  `image_name` varchar(255) DEFAULT NULL COMMENT '图片名称',
  `image_size` bigint(20) DEFAULT NULL COMMENT '图片大小（字节）',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`image_id`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP任务图片表';

-- 修改任务表，添加任务类型字段
ALTER TABLE `app_task` 
ADD COLUMN `first_type_id` bigint(20) DEFAULT NULL COMMENT '一级任务类型ID' AFTER `task_type`,
ADD COLUMN `second_type_id` bigint(20) DEFAULT NULL COMMENT '二级任务类型ID' AFTER `first_type_id`,
ADD COLUMN `urgent_level` char(1) DEFAULT '0' COMMENT '紧急程度（0普通 1紧急 2非常紧急）' AFTER `second_type_id`;

-- 插入默认任务类型数据
INSERT INTO `app_task_type` (`type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`) VALUES
('生活服务', 0, '/static/icons/life.png', 1, '0', '日常生活相关服务', 'admin', NOW()),
('跑腿代办', 0, '/static/icons/errand.png', 2, '0', '跑腿代办相关服务', 'admin', NOW()),
('技能服务', 0, '/static/icons/skill.png', 3, '0', '专业技能相关服务', 'admin', NOW()),
('搬运装卸', 0, '/static/icons/moving.png', 4, '0', '搬运装卸相关服务', 'admin', NOW()),
('其他服务', 0, '/static/icons/other.png', 5, '0', '其他类型服务', 'admin', NOW());

-- 插入二级任务类型数据
INSERT INTO `app_task_type` (`type_name`, `parent_id`, `type_icon`, `order_num`, `status`, `type_desc`, `create_by`, `create_time`) VALUES
-- 生活服务子类型
('家政清洁', 1, '/static/icons/cleaning.png', 1, '0', '家庭清洁服务', 'admin', NOW()),
('维修安装', 1, '/static/icons/repair.png', 2, '0', '家电维修安装', 'admin', NOW()),
('宠物服务', 1, '/static/icons/pet.png', 3, '0', '宠物相关服务', 'admin', NOW()),
('照看陪护', 1, '/static/icons/care.png', 4, '0', '照看陪护服务', 'admin', NOW()),

-- 跑腿代办子类型
('代买代购', 2, '/static/icons/shopping.png', 1, '0', '代买代购服务', 'admin', NOW()),
('文件递送', 2, '/static/icons/delivery.png', 2, '0', '文件递送服务', 'admin', NOW()),
('排队代办', 2, '/static/icons/queue.png', 3, '0', '排队代办服务', 'admin', NOW()),
('取送物品', 2, '/static/icons/pickup.png', 4, '0', '取送物品服务', 'admin', NOW()),

-- 技能服务子类型
('设计制作', 3, '/static/icons/design.png', 1, '0', '设计制作服务', 'admin', NOW()),
('教学培训', 3, '/static/icons/teaching.png', 2, '0', '教学培训服务', 'admin', NOW()),
('翻译服务', 3, '/static/icons/translate.png', 3, '0', '翻译服务', 'admin', NOW()),
('咨询服务', 3, '/static/icons/consult.png', 4, '0', '咨询服务', 'admin', NOW()),

-- 搬运装卸子类型
('搬家服务', 4, '/static/icons/move.png', 1, '0', '搬家服务', 'admin', NOW()),
('货物装卸', 4, '/static/icons/loading.png', 2, '0', '货物装卸服务', 'admin', NOW()),
('家具安装', 4, '/static/icons/furniture.png', 3, '0', '家具安装服务', 'admin', NOW()),

-- 其他服务子类型
('临时帮工', 5, '/static/icons/temp.png', 1, '0', '临时帮工服务', 'admin', NOW()),
('活动协助', 5, '/static/icons/event.png', 2, '0', '活动协助服务', 'admin', NOW()),
('其他需求', 5, '/static/icons/others.png', 3, '0', '其他需求服务', 'admin', NOW());
