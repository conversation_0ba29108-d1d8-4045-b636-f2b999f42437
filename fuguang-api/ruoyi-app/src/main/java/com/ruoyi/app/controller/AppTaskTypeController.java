package com.ruoyi.app.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppTaskType;
import com.ruoyi.fuguang.service.IAppTaskTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * APP任务类型Controller
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
@Api(tags = "APP任务类型接口")
@RestController("appTaskTypeApiController")
@RequestMapping("/app/taskType")
public class AppTaskTypeController extends BaseController
{
    @Autowired
    private IAppTaskTypeService appTaskTypeService;

    /**
     * 查询APP任务类型列表
     */
    @Anonymous
    @ApiOperation("查询任务类型列表")
    @GetMapping("/list")
    public TableDataInfo list(AppTaskType appTaskType)
    {
        startPage();
        List<AppTaskType> list = appTaskTypeService.selectAppTaskTypeList(appTaskType);
        return getDataTable(list);
    }

    /**
     * 获取APP任务类型详细信息
     */
    @Anonymous
    @ApiOperation("获取任务类型详细信息")
    @GetMapping(value = "/{typeId}")
    public AjaxResult getInfo(@PathVariable("typeId") Long typeId)
    {
        return success(appTaskTypeService.selectAppTaskTypeByTypeId(typeId));
    }

    /**
     * 获取一级任务类型列表
     */
    @Anonymous
    @ApiOperation("获取一级任务类型列表")
    @GetMapping("/firstLevel")
    public AjaxResult getFirstLevelTypes()
    {
        List<AppTaskType> list = appTaskTypeService.selectFirstLevelTaskTypes();
        return success(list);
    }

    /**
     * 根据父类型ID获取子类型列表
     */
    @Anonymous
    @ApiOperation("根据父类型ID获取子类型列表")
    @GetMapping("/children/{parentId}")
    public AjaxResult getChildrenTypes(@PathVariable("parentId") Long parentId)
    {
        List<AppTaskType> list = appTaskTypeService.selectTaskTypesByParentId(parentId);
        return success(list);
    }

    /**
     * 获取任务类型树形结构
     */
    @Anonymous
    @ApiOperation("获取任务类型树形结构")
    @GetMapping("/tree")
    public AjaxResult getTaskTypeTree()
    {
        List<AppTaskType> list = appTaskTypeService.buildTaskTypeTree();
        return success(list);
    }
}
