package com.ruoyi.app.controller;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppUserAddress;
import com.ruoyi.fuguang.service.IAppUserAddressService;
import com.ruoyi.common.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * APP用户地址Controller
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
@Api(tags = "APP用户地址管理")
@RestController("appUserAddressApiController")
@RequestMapping("/app/address")
public class AppUserAddressController extends BaseController
{
    @Autowired
    private IAppUserAddressService appUserAddressService;

    /**
     * 查询当前用户地址列表
     */
    @ApiOperation("查询当前用户地址列表")
    @GetMapping("/list")
    public AjaxResult list()
    {
        Long userId = getUserId();
        List<AppUserAddress> list = appUserAddressService.selectAppUserAddressListByUserId(userId);
        return success(list);
    }

    /**
     * 获取地址详细信息
     */
    @ApiOperation("获取地址详细信息")
    @GetMapping(value = "/{addressId}")
    public AjaxResult getInfo(@PathVariable("addressId") Long addressId)
    {
        Long userId = getUserId();
        AppUserAddress address = appUserAddressService.selectAppUserAddressByAddressId(addressId);
        
        // 检查地址是否属于当前用户
        if (address == null || !userId.equals(address.getUserId()))
        {
            return error("地址不存在或无权限访问");
        }
        
        return success(address);
    }

    /**
     * 新增地址
     */
    @ApiOperation("新增地址")
    @Log(title = "APP用户地址", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUserAddress appUserAddress)
    {
        Long userId = getUserId();
        appUserAddress.setUserId(userId);
        
        // 数据校验
        String validateResult = appUserAddressService.validateAddressData(appUserAddress);
        if (StringUtils.isNotEmpty(validateResult))
        {
            return error(validateResult);
        }
        
        // 检查联系人手机号是否唯一（同一用户下）
        if (!appUserAddressService.checkContactPhoneUnique(appUserAddress))
        {
            return error("新增地址失败，联系人手机号已存在");
        }
        
        appUserAddress.setCreateBy(String.valueOf(userId));
        int result = appUserAddressService.insertAppUserAddress(appUserAddress);
        
        if (result > 0)
        {
            return success("新增成功");
        }
        return error("新增失败");
    }

    /**
     * 修改地址
     */
    @ApiOperation("修改地址")
    @Log(title = "APP用户地址", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUserAddress appUserAddress)
    {
        Long userId = getUserId();
        
        // 检查地址是否属于当前用户
        AppUserAddress existAddress = appUserAddressService.selectAppUserAddressByAddressId(appUserAddress.getAddressId());
        if (existAddress == null || !userId.equals(existAddress.getUserId()))
        {
            return error("地址不存在或无权限修改");
        }
        
        appUserAddress.setUserId(userId);
        
        // 数据校验
        String validateResult = appUserAddressService.validateAddressData(appUserAddress);
        if (StringUtils.isNotEmpty(validateResult))
        {
            return error(validateResult);
        }
        
        // 检查联系人手机号是否唯一（同一用户下）
        if (!appUserAddressService.checkContactPhoneUnique(appUserAddress))
        {
            return error("修改地址失败，联系人手机号已存在");
        }
        
        appUserAddress.setUpdateBy(String.valueOf(userId));
        int result = appUserAddressService.updateAppUserAddress(appUserAddress);
        
        if (result > 0)
        {
            return success("修改成功");
        }
        return error("修改失败");
    }

    /**
     * 删除地址
     */
    @ApiOperation("删除地址")
    @Log(title = "APP用户地址", businessType = BusinessType.DELETE)
    @DeleteMapping("/{addressId}")
    public AjaxResult remove(@PathVariable Long addressId)
    {
        Long userId = getUserId();
        
        // 检查地址是否属于当前用户
        AppUserAddress address = appUserAddressService.selectAppUserAddressByAddressId(addressId);
        if (address == null || !userId.equals(address.getUserId()))
        {
            return error("地址不存在或无权限删除");
        }
        
        int result = appUserAddressService.deleteAppUserAddressByAddressId(addressId);
        
        if (result > 0)
        {
            return success("删除成功");
        }
        return error("删除失败");
    }

    /**
     * 设置默认地址
     */
    @ApiOperation("设置默认地址")
    @Log(title = "APP用户地址", businessType = BusinessType.UPDATE)
    @PutMapping("/setDefault/{addressId}")
    public AjaxResult setDefault(@PathVariable Long addressId)
    {
        Long userId = getUserId();
        
        // 检查地址是否属于当前用户
        AppUserAddress address = appUserAddressService.selectAppUserAddressByAddressId(addressId);
        if (address == null || !userId.equals(address.getUserId()))
        {
            return error("地址不存在或无权限设置");
        }
        
        int result = appUserAddressService.setDefaultAddress(addressId, userId);
        
        if (result > 0)
        {
            return success("设置成功");
        }
        return error("设置失败");
    }

    /**
     * 获取默认地址
     */
    @ApiOperation("获取默认地址")
    @GetMapping("/default")
    public AjaxResult getDefaultAddress()
    {
        Long userId = getUserId();
        AppUserAddress address = appUserAddressService.selectDefaultAddressByUserId(userId);
        return success(address);
    }

    /**
     * 根据定位信息解析地址
     */
    @ApiOperation("根据定位信息解析地址")
    @PostMapping("/parseLocation")
    public AjaxResult parseLocation(@RequestBody AppUserAddress location)
    {
        // 这里可以集成第三方地图服务API来解析经纬度为具体地址
        // 目前返回基本信息
        if (StringUtils.isEmpty(location.getLongitude()) || StringUtils.isEmpty(location.getLatitude()))
        {
            return error("经纬度信息不能为空");
        }
        
        // TODO: 集成地图服务API解析地址
        // 暂时返回经纬度信息
        return success("定位成功", location);
    }
}
